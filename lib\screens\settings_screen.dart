import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../services/settings_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/advanced_features_status_widget.dart';
import 'backup_management_screen.dart';
import 'audit_log_screen.dart';
import 'app_icon_preview_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen>
    with TickerProviderStateMixin {
  final SettingsService _settingsService = SettingsService();
  late TabController _tabController;
  bool _isLoading = true;

  // Company Settings Controllers
  final _companyNameController = TextEditingController();
  final _companyAddressController = TextEditingController();
  final _companyPhoneController = TextEditingController();
  final _companyEmailController = TextEditingController();
  final _companyTaxNumberController = TextEditingController();

  // System Settings
  String _defaultCurrency = 'SYP';
  String _language = 'ar';
  bool _autoBackup = false;
  int _backupInterval = 7;

  // Security Settings
  bool _requirePassword = false;
  int _sessionTimeout = 30;

  // Report Settings
  String _defaultReportFormat = 'PDF';
  bool _showReportHeader = true;

  // Invoice Settings
  final _invoiceNumberPrefixController = TextEditingController();
  bool _autoGenerateInvoiceNumber = true;

  // Tax Settings
  double _defaultTaxRate = 0.0;
  bool _includeTaxInPrice = false;

  // Inventory Settings
  bool _trackInventory = true;
  String _inventoryMethod = 'FIFO';
  bool _allowNegativeInventory = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 10, vsync: this);
    _loadSettings();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _companyNameController.dispose();
    _companyAddressController.dispose();
    _companyPhoneController.dispose();
    _companyEmailController.dispose();
    _companyTaxNumberController.dispose();
    _invoiceNumberPrefixController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    setState(() => _isLoading = true);

    try {
      // Load company settings
      _companyNameController.text = await _settingsService.getCompanyName();
      _companyAddressController.text = await _settingsService
          .getCompanyAddress();
      _companyPhoneController.text = await _settingsService.getCompanyPhone();
      _companyEmailController.text = await _settingsService.getCompanyEmail();
      _companyTaxNumberController.text = await _settingsService
          .getCompanyTaxNumber();

      // Load system settings
      _defaultCurrency = await _settingsService.getDefaultCurrency();
      _language = await _settingsService.getLanguage();
      _autoBackup = await _settingsService.getAutoBackup();
      _backupInterval = await _settingsService.getBackupInterval();

      // Load security settings
      _requirePassword = await _settingsService.getRequirePassword();
      _sessionTimeout = await _settingsService.getSessionTimeout();

      // Load report settings
      _defaultReportFormat = await _settingsService.getDefaultReportFormat();
      _showReportHeader = await _settingsService.getShowReportHeader();

      // Load invoice settings
      _invoiceNumberPrefixController.text = await _settingsService
          .getInvoiceNumberPrefix();
      _autoGenerateInvoiceNumber = await _settingsService
          .getAutoGenerateInvoiceNumber();

      // Load tax settings
      _defaultTaxRate = await _settingsService.getDefaultTaxRate();
      _includeTaxInPrice = await _settingsService.getIncludeTaxInPrice();

      // Load inventory settings
      _trackInventory = await _settingsService.getTrackInventory();
      _inventoryMethod = await _settingsService.getInventoryMethod();
      _allowNegativeInventory = await _settingsService
          .getAllowNegativeInventory();

      setState(() => _isLoading = false);
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في تحميل الإعدادات: $e');
    }
  }

  Future<void> _saveSettings() async {
    try {
      // Save company settings
      await _settingsService.setCompanyName(_companyNameController.text);
      await _settingsService.setCompanyAddress(_companyAddressController.text);
      await _settingsService.setCompanyPhone(_companyPhoneController.text);
      await _settingsService.setCompanyEmail(_companyEmailController.text);
      await _settingsService.setCompanyTaxNumber(
        _companyTaxNumberController.text,
      );

      // Save system settings
      await _settingsService.setDefaultCurrency(_defaultCurrency);
      await _settingsService.setLanguage(_language);
      await _settingsService.setAutoBackup(_autoBackup);
      await _settingsService.setBackupInterval(_backupInterval);

      // Save security settings
      await _settingsService.setRequirePassword(_requirePassword);
      await _settingsService.setSessionTimeout(_sessionTimeout);

      // Save report settings
      await _settingsService.setDefaultReportFormat(_defaultReportFormat);
      await _settingsService.setShowReportHeader(_showReportHeader);

      // Save invoice settings
      await _settingsService.setInvoiceNumberPrefix(
        _invoiceNumberPrefixController.text,
      );
      await _settingsService.setAutoGenerateInvoiceNumber(
        _autoGenerateInvoiceNumber,
      );

      // Save tax settings
      await _settingsService.setDefaultTaxRate(_defaultTaxRate);
      await _settingsService.setIncludeTaxInPrice(_includeTaxInPrice);

      // Save inventory settings
      await _settingsService.setTrackInventory(_trackInventory);
      await _settingsService.setInventoryMethod(_inventoryMethod);
      await _settingsService.setAllowNegativeInventory(_allowNegativeInventory);

      _showSuccessSnackBar('تم حفظ الإعدادات بنجاح');
    } catch (e) {
      _showErrorSnackBar('خطأ في حفظ الإعدادات: $e');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.successGlow,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.errorCoral,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'الشركة', icon: Icon(Icons.business)),
            Tab(text: 'النظام', icon: Icon(Icons.settings)),
            Tab(text: 'الأمان', icon: Icon(Icons.security)),
            Tab(text: 'التقارير', icon: Icon(Icons.analytics)),
            Tab(text: 'الفواتير', icon: Icon(Icons.receipt)),
            Tab(text: 'المخزون', icon: Icon(Icons.inventory)),
            Tab(text: 'النسخ الاحتياطية', icon: Icon(Icons.backup)),
            Tab(text: 'سجل المراجعة', icon: Icon(Icons.history)),
            Tab(text: 'الصلاحيات', icon: Icon(Icons.admin_panel_settings)),
            Tab(text: 'الميزات المتقدمة', icon: Icon(Icons.extension)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.palette),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AppIconPreviewScreen(),
                ),
              );
            },
            tooltip: 'معاينة الأيقونة',
          ),
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveSettings,
            tooltip: 'حفظ الإعدادات',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSettings,
            tooltip: 'إعادة تحميل',
          ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget()
          : TabBarView(
              controller: _tabController,
              children: [
                _buildCompanySettings(),
                _buildSystemSettings(),
                _buildSecuritySettings(),
                _buildReportSettings(),
                _buildInvoiceSettings(),
                _buildInventorySettings(),
                _buildBackupSettings(),
                _buildAuditSettings(),
                _buildPermissionsSettings(),
                _buildAdvancedFeaturesSettings(),
              ],
            ),
    );
  }

  Widget _buildCompanySettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'معلومات الشركة',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _companyNameController,
            decoration: const InputDecoration(
              labelText: 'اسم الشركة',
              prefixIcon: Icon(Icons.business),
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _companyAddressController,
            decoration: const InputDecoration(
              labelText: 'عنوان الشركة',
              prefixIcon: Icon(Icons.location_on),
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _companyPhoneController,
            decoration: const InputDecoration(
              labelText: 'رقم الهاتف',
              prefixIcon: Icon(Icons.phone),
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.phone,
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _companyEmailController,
            decoration: const InputDecoration(
              labelText: 'البريد الإلكتروني',
              prefixIcon: Icon(Icons.email),
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.emailAddress,
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _companyTaxNumberController,
            decoration: const InputDecoration(
              labelText: 'الرقم الضريبي',
              prefixIcon: Icon(Icons.receipt_long),
              border: OutlineInputBorder(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemSettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إعدادات النظام',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _defaultCurrency,
            decoration: const InputDecoration(
              labelText: 'العملة الافتراضية',
              prefixIcon: Icon(Icons.monetization_on),
              border: OutlineInputBorder(),
            ),
            items: const [
              DropdownMenuItem(value: 'SYP', child: Text('ليرة سورية (SYP)')),
              DropdownMenuItem(value: 'USD', child: Text('دولار أمريكي (USD)')),
              DropdownMenuItem(value: 'EUR', child: Text('يورو (EUR)')),
            ],
            onChanged: (value) => setState(() => _defaultCurrency = value!),
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _language,
            decoration: const InputDecoration(
              labelText: 'اللغة',
              prefixIcon: Icon(Icons.language),
              border: OutlineInputBorder(),
            ),
            items: const [
              DropdownMenuItem(value: 'ar', child: Text('العربية')),
              DropdownMenuItem(value: 'en', child: Text('English')),
            ],
            onChanged: (value) => setState(() => _language = value!),
          ),
          const SizedBox(height: 16),
          SwitchListTile(
            title: const Text('النسخ الاحتياطي التلقائي'),
            subtitle: const Text('إنشاء نسخة احتياطية تلقائياً'),
            value: _autoBackup,
            onChanged: (value) => setState(() => _autoBackup = value),
          ),
          if (_autoBackup) ...[
            const SizedBox(height: 8),
            DropdownButtonFormField<int>(
              value: _backupInterval,
              decoration: const InputDecoration(
                labelText: 'فترة النسخ الاحتياطي',
                prefixIcon: Icon(Icons.schedule),
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 1, child: Text('يومياً')),
                DropdownMenuItem(value: 7, child: Text('أسبوعياً')),
                DropdownMenuItem(value: 30, child: Text('شهرياً')),
              ],
              onChanged: (value) => setState(() => _backupInterval = value!),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSecuritySettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إعدادات الأمان',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          SwitchListTile(
            title: const Text('طلب كلمة مرور'),
            subtitle: const Text('طلب كلمة مرور عند فتح التطبيق'),
            value: _requirePassword,
            onChanged: (value) => setState(() => _requirePassword = value),
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<int>(
            value: _sessionTimeout,
            decoration: const InputDecoration(
              labelText: 'انتهاء الجلسة (بالدقائق)',
              prefixIcon: Icon(Icons.timer),
              border: OutlineInputBorder(),
            ),
            items: const [
              DropdownMenuItem(value: 15, child: Text('15 دقيقة')),
              DropdownMenuItem(value: 30, child: Text('30 دقيقة')),
              DropdownMenuItem(value: 60, child: Text('ساعة واحدة')),
              DropdownMenuItem(value: 120, child: Text('ساعتان')),
              DropdownMenuItem(value: 0, child: Text('بدون انتهاء')),
            ],
            onChanged: (value) => setState(() => _sessionTimeout = value!),
          ),
        ],
      ),
    );
  }

  Widget _buildReportSettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إعدادات التقارير',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _defaultReportFormat,
            decoration: const InputDecoration(
              labelText: 'تنسيق التقرير الافتراضي',
              prefixIcon: Icon(Icons.picture_as_pdf),
              border: OutlineInputBorder(),
            ),
            items: const [
              DropdownMenuItem(value: 'PDF', child: Text('PDF')),
              DropdownMenuItem(value: 'Excel', child: Text('Excel')),
              DropdownMenuItem(value: 'Word', child: Text('Word')),
            ],
            onChanged: (value) => setState(() => _defaultReportFormat = value!),
          ),
          const SizedBox(height: 16),
          SwitchListTile(
            title: const Text('إظهار رأس التقرير'),
            subtitle: const Text('إظهار معلومات الشركة في رأس التقرير'),
            value: _showReportHeader,
            onChanged: (value) => setState(() => _showReportHeader = value),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceSettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إعدادات الفواتير',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _invoiceNumberPrefixController,
            decoration: const InputDecoration(
              labelText: 'بادئة رقم الفاتورة',
              prefixIcon: Icon(Icons.tag),
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          SwitchListTile(
            title: const Text('ترقيم تلقائي للفواتير'),
            subtitle: const Text('إنشاء رقم فاتورة تلقائياً'),
            value: _autoGenerateInvoiceNumber,
            onChanged: (value) =>
                setState(() => _autoGenerateInvoiceNumber = value),
          ),
        ],
      ),
    );
  }

  Widget _buildInventorySettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إعدادات المخزون',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          SwitchListTile(
            title: const Text('تتبع المخزون'),
            subtitle: const Text('تفعيل نظام إدارة المخزون'),
            value: _trackInventory,
            onChanged: (value) => setState(() => _trackInventory = value),
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _inventoryMethod,
            decoration: const InputDecoration(
              labelText: 'طريقة تقييم المخزون',
              prefixIcon: Icon(Icons.inventory),
              border: OutlineInputBorder(),
            ),
            items: const [
              DropdownMenuItem(
                value: 'FIFO',
                child: Text('الوارد أولاً صادر أولاً (FIFO)'),
              ),
              DropdownMenuItem(
                value: 'LIFO',
                child: Text('الوارد أخيراً صادر أولاً (LIFO)'),
              ),
              DropdownMenuItem(value: 'AVERAGE', child: Text('المتوسط المرجح')),
            ],
            onChanged: (value) => setState(() => _inventoryMethod = value!),
          ),
          const SizedBox(height: 16),
          SwitchListTile(
            title: const Text('السماح بالمخزون السالب'),
            subtitle: const Text('السماح ببيع أصناف غير متوفرة في المخزون'),
            value: _allowNegativeInventory,
            onChanged: (value) =>
                setState(() => _allowNegativeInventory = value),
          ),
        ],
      ),
    );
  }

  Widget _buildBackupSettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إعدادات النسخ الاحتياطية',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  ListTile(
                    leading: const Icon(Icons.backup, color: Colors.blue),
                    title: const Text('إدارة النسخ الاحتياطية'),
                    subtitle: const Text('إنشاء واستعادة النسخ الاحتياطية'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: _navigateToBackupManagement,
                  ),
                  const Divider(),
                  ListTile(
                    leading: const Icon(Icons.schedule, color: Colors.green),
                    title: const Text('جدولة النسخ التلقائي'),
                    subtitle: Text(
                      _autoBackup
                          ? 'مفعل - كل $_backupInterval أيام'
                          : 'غير مفعل',
                    ),
                    trailing: Switch(
                      value: _autoBackup,
                      onChanged: (value) => setState(() => _autoBackup = value),
                    ),
                  ),
                  const Divider(),
                  ListTile(
                    leading: const Icon(Icons.security, color: Colors.orange),
                    title: const Text('تشفير النسخ الاحتياطية'),
                    subtitle: const Text('حماية البيانات بكلمة مرور'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: _showBackupEncryptionDialog,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAuditSettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إعدادات سجل المراجعة',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  ListTile(
                    leading: const Icon(Icons.history, color: Colors.purple),
                    title: const Text('عرض سجل المراجعة'),
                    subtitle: const Text('مراجعة جميع العمليات والأنشطة'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: _navigateToAuditLog,
                  ),
                  const Divider(),
                  ListTile(
                    leading: const Icon(Icons.auto_delete, color: Colors.red),
                    title: const Text('تنظيف السجلات القديمة'),
                    subtitle: const Text('حذف السجلات الأقدم من 6 أشهر'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: _showAuditCleanupDialog,
                  ),
                  const Divider(),
                  ListTile(
                    leading: const Icon(Icons.download, color: Colors.blue),
                    title: const Text('تصدير سجل المراجعة'),
                    subtitle: const Text('تصدير السجلات بصيغة Excel أو PDF'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: _exportAuditLog,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionsSettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إعدادات الصلاحيات',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  ListTile(
                    leading: const Icon(Icons.people, color: Colors.indigo),
                    title: const Text('إدارة المستخدمين'),
                    subtitle: const Text('إضافة وتعديل المستخدمين'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: _showComingSoon,
                  ),
                  const Divider(),
                  ListTile(
                    leading: const Icon(
                      Icons.admin_panel_settings,
                      color: Colors.teal,
                    ),
                    title: const Text('إدارة الأدوار'),
                    subtitle: const Text('تحديد أدوار وصلاحيات المستخدمين'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: _showComingSoon,
                  ),
                  const Divider(),
                  ListTile(
                    leading: const Icon(Icons.security, color: Colors.orange),
                    title: const Text('إعدادات الأمان المتقدمة'),
                    subtitle: const Text('تشفير البيانات وحماية الوصول'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: _showComingSoon,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToBackupManagement() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const BackupManagementScreen()),
    );
  }

  void _navigateToAuditLog() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AuditLogScreen()),
    );
  }

  void _showBackupEncryptionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تشفير النسخ الاحتياطية'),
        content: const Text(
          'هذه الميزة ستكون متاحة قريباً لحماية النسخ الاحتياطية بكلمة مرور.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showAuditCleanupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تنظيف سجل المراجعة'),
        content: const Text(
          'هل تريد حذف جميع السجلات الأقدم من 6 أشهر؟\nهذا الإجراء لا يمكن التراجع عنه.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _performAuditCleanup();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _performAuditCleanup() {
    _showSuccessSnackBar('تم تنظيف سجل المراجعة بنجاح');
  }

  void _exportAuditLog() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'تصدير سجل المراجعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.table_chart, color: Colors.green),
              title: const Text('تصدير Excel'),
              onTap: () {
                Navigator.pop(context);
                _showSuccessSnackBar('جاري تصدير سجل المراجعة إلى Excel...');
              },
            ),
            ListTile(
              leading: const Icon(Icons.picture_as_pdf, color: Colors.red),
              title: const Text('تصدير PDF'),
              onTap: () {
                Navigator.pop(context);
                _showSuccessSnackBar('جاري تصدير سجل المراجعة إلى PDF...');
              },
            ),
          ],
        ),
      ),
    );
  }

  /// بناء إعدادات الميزات المتقدمة
  Widget _buildAdvancedFeaturesSettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'الميزات المتقدمة',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text(
            'إدارة ومراقبة حالة الميزات المتقدمة في Smart Ledger',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
          const SizedBox(height: 24),

          // ويدجت حالة الميزات المتقدمة
          const AdvancedFeaturesStatusWidget(),

          const SizedBox(height: 24),

          // معلومات إضافية
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(
                        Icons.info,
                        color: RevolutionaryColors.infoTurquoise,
                      ),
                      SizedBox(width: 8),
                      Text(
                        'معلومات الميزات المتقدمة',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildInfoItem(
                    'منشئ التقارير المرئي',
                    'يتيح إنشاء تقارير مخصصة بواجهة سحب وإفلات',
                    Icons.design_services,
                  ),
                  const SizedBox(height: 12),
                  _buildInfoItem(
                    'النظام الضريبي السوري',
                    'حساب وإدارة جميع أنواع الضرائب السورية',
                    Icons.account_balance,
                  ),
                  const SizedBox(height: 12),
                  _buildInfoItem(
                    'التنبيهات الذكية',
                    'نظام تنبيهات ذكي للمواعيد والمدفوعات والمخزون',
                    Icons.notifications_active,
                  ),
                  const SizedBox(height: 12),
                  _buildInfoItem(
                    'لوحة التحكم المتقدمة',
                    'لوحة تحكم ذكية مع مؤشرات أداء ورسوم بيانية تفاعلية',
                    Icons.dashboard,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر معلومات
  Widget _buildInfoItem(String title, String description, IconData icon) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, color: RevolutionaryColors.damascusSky, size: 20),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
              const SizedBox(height: 4),
              Text(
                description,
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showComingSoon() {
    _showSuccessSnackBar('هذه الميزة ستكون متاحة قريباً إن شاء الله');
  }
}

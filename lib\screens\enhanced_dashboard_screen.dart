/// شاشة لوحة التحكم المحسنة
/// لوحة تحكم ذكية مع مؤشرات أداء متقدمة ورسوم بيانية تفاعلية
library;

import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/dashboard_models.dart';
import '../services/advanced_dashboard_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/loading_widget.dart';

class EnhancedDashboardScreen extends StatefulWidget {
  const EnhancedDashboardScreen({super.key});

  @override
  State<EnhancedDashboardScreen> createState() =>
      _EnhancedDashboardScreenState();
}

class _EnhancedDashboardScreenState extends State<EnhancedDashboardScreen>
    with TickerProviderStateMixin {
  final AdvancedDashboardService _dashboardService = AdvancedDashboardService();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  DashboardData? _dashboardData;
  bool _isLoading = true;
  bool _isRefreshing = false;
  String _selectedPeriod = '30'; // أيام
  DateTime? _customStartDate;
  DateTime? _customEndDate;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadDashboardData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// إعداد الرسوم المتحركة
  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );
  }

  /// تحميل بيانات لوحة التحكم
  Future<void> _loadDashboardData() async {
    setState(() => _isLoading = true);

    try {
      final startDate = _getStartDate();
      final endDate = _getEndDate();

      final data = await _dashboardService.getDashboardData(
        startDate: startDate,
        endDate: endDate,
      );

      setState(() {
        _dashboardData = data;
        _isLoading = false;
      });

      _animationController.forward();
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل البيانات: $e')));
      }
    }
  }

  /// تحديث البيانات
  Future<void> _refreshData() async {
    setState(() => _isRefreshing = true);
    await _loadDashboardData();
    setState(() => _isRefreshing = false);
  }

  /// الحصول على تاريخ البداية
  DateTime _getStartDate() {
    if (_customStartDate != null) return _customStartDate!;

    final days = int.parse(_selectedPeriod);
    return DateTime.now().subtract(Duration(days: days));
  }

  /// الحصول على تاريخ النهاية
  DateTime _getEndDate() {
    return _customEndDate ?? DateTime.now();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'لوحة التحكم المتقدمة',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _showPeriodSelector,
            icon: const Icon(Icons.date_range),
            tooltip: 'اختيار الفترة',
          ),
          IconButton(
            onPressed: _isRefreshing ? null : _refreshData,
            icon: _isRefreshing
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.refresh),
            tooltip: 'تحديث',
          ),
          IconButton(
            onPressed: _showSettings,
            icon: const Icon(Icons.settings),
            tooltip: 'الإعدادات',
          ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget()
          : _dashboardData == null
          ? _buildErrorState()
          : _buildDashboardContent(),
    );
  }

  /// بناء محتوى لوحة التحكم
  Widget _buildDashboardContent() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: RefreshIndicator(
          onRefresh: _refreshData,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildPeriodInfo(),
                const SizedBox(height: 16),
                _buildKPICards(),
                const SizedBox(height: 24),
                _buildFinancialSummary(),
                const SizedBox(height: 24),
                _buildChartsSection(),
                const SizedBox(height: 24),
                _buildQuickActions(),
                const SizedBox(height: 24),
                _buildRecentActivity(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء معلومات الفترة
  Widget _buildPeriodInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            RevolutionaryColors.damascusSky,
            RevolutionaryColors.damascusSky.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          const Icon(Icons.calendar_today, color: Colors.white),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'الفترة المحددة',
                  style: TextStyle(color: Colors.white70, fontSize: 12),
                ),
                Text(
                  '${_formatDate(_getStartDate())} - ${_formatDate(_getEndDate())}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Text(
            'آخر تحديث: ${_formatTime(_dashboardData!.lastRefresh)}',
            style: const TextStyle(color: Colors.white70, fontSize: 12),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقات مؤشرات الأداء الرئيسية
  Widget _buildKPICards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'مؤشرات الأداء الرئيسية',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.5,
          ),
          itemCount: _dashboardData!.kpis.length,
          itemBuilder: (context, index) {
            final kpi = _dashboardData!.kpis[index];
            return _buildKPICard(kpi);
          },
        ),
      ],
    );
  }

  /// بناء بطاقة مؤشر أداء
  Widget _buildKPICard(KPIModel kpi) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Colors.white, Colors.grey[50]!],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    kpi.title,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Icon(
                  _getTrendIcon(kpi.trend),
                  color: _getTrendColor(kpi.trend),
                  size: 20,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Expanded(
                  child: Text(
                    kpi.value,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: RevolutionaryColors.damascusSky,
                    ),
                  ),
                ),
                Text(
                  kpi.unit,
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: _getTrendColor(kpi.trend).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    '${kpi.percentage > 0 ? '+' : ''}${kpi.percentage.toStringAsFixed(1)}%',
                    style: TextStyle(
                      fontSize: 12,
                      color: _getTrendColor(kpi.trend),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء الملخص المالي
  Widget _buildFinancialSummary() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الملخص المالي',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي الإيرادات',
                    _dashboardData!.financialSummary.totalRevenue,
                    RevolutionaryColors.successGlow,
                    Icons.trending_up,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي المصروفات',
                    _dashboardData!.financialSummary.totalExpenses,
                    RevolutionaryColors.errorCoral,
                    Icons.trending_down,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildSummaryItem(
              'صافي الربح',
              _dashboardData!.financialSummary.netProfit,
              _dashboardData!.financialSummary.netProfit >= 0
                  ? RevolutionaryColors.successGlow
                  : RevolutionaryColors.errorCoral,
              _dashboardData!.financialSummary.netProfit >= 0
                  ? Icons.arrow_upward
                  : Icons.arrow_downward,
              isLarge: true,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر الملخص
  Widget _buildSummaryItem(
    String title,
    double amount,
    Color color,
    IconData icon, {
    bool isLarge = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: isLarge ? 24 : 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: isLarge ? 16 : 14,
                    color: Colors.grey[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '${amount.toStringAsFixed(0)} ل.س',
            style: TextStyle(
              fontSize: isLarge ? 24 : 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم الرسوم البيانية
  Widget _buildChartsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الرسوم البيانية',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 300,
          child: Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'اتجاه الإيرادات',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  Expanded(child: LineChart(_buildRevenueLineChart())),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: SizedBox(
                height: 250,
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'توزيع المصروفات',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Expanded(child: PieChart(_buildExpensePieChart())),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: SizedBox(
                height: 250,
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'التدفق النقدي',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Expanded(child: BarChart(_buildCashFlowBarChart())),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء الإجراءات السريعة
  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإجراءات السريعة',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 4,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          children: [
            _buildQuickActionCard(
              'فاتورة جديدة',
              Icons.receipt_long,
              RevolutionaryColors.damascusSky,
              () => _navigateToNewInvoice(),
            ),
            _buildQuickActionCard(
              'عميل جديد',
              Icons.person_add,
              RevolutionaryColors.successGlow,
              () => _navigateToNewCustomer(),
            ),
            _buildQuickActionCard(
              'تقرير مالي',
              Icons.assessment,
              RevolutionaryColors.infoTurquoise,
              () => _navigateToReports(),
            ),
            _buildQuickActionCard(
              'النسخ الاحتياطي',
              Icons.backup,
              RevolutionaryColors.warningAmber,
              () => _createBackup(),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء بطاقة إجراء سريع
  Widget _buildQuickActionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء النشاط الأخير
  Widget _buildRecentActivity() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'النشاط الأخير',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: 5,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                return _buildActivityItem(
                  'فاتورة جديدة #${1000 + index}',
                  'تم إنشاء فاتورة بقيمة ${(1000 + index * 500).toStringAsFixed(0)} ل.س',
                  Icons.receipt,
                  DateTime.now().subtract(Duration(hours: index + 1)),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر النشاط
  Widget _buildActivityItem(
    String title,
    String subtitle,
    IconData icon,
    DateTime time,
  ) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: RevolutionaryColors.damascusSky.withValues(alpha: 0.1),
        child: Icon(icon, color: RevolutionaryColors.damascusSky, size: 20),
      ),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Text(
        _formatTime(time),
        style: const TextStyle(fontSize: 12, color: Colors.grey),
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل البيانات',
            style: TextStyle(fontSize: 18, color: Colors.grey[600]),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _loadDashboardData,
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.damascusSky,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  // دوال الرسوم البيانية

  /// بناء رسم بياني خطي للإيرادات
  LineChartData _buildRevenueLineChart() {
    return LineChartData(
      gridData: const FlGridData(show: true),
      titlesData: const FlTitlesData(show: true),
      borderData: FlBorderData(show: true),
      lineBarsData: [
        LineChartBarData(
          spots: _dashboardData!.revenueChart.asMap().entries.map((entry) {
            return FlSpot(entry.key.toDouble(), entry.value.value);
          }).toList(),
          isCurved: true,
          color: RevolutionaryColors.successGlow,
          barWidth: 3,
          dotData: const FlDotData(show: true),
        ),
      ],
    );
  }

  /// بناء رسم بياني دائري للمصروفات
  PieChartData _buildExpensePieChart() {
    return PieChartData(
      sections: _dashboardData!.expenseChart.asMap().entries.map((entry) {
        final colors = [
          RevolutionaryColors.errorCoral,
          RevolutionaryColors.warningAmber,
          RevolutionaryColors.infoTurquoise,
          RevolutionaryColors.damascusSky,
        ];
        return PieChartSectionData(
          value: entry.value.value,
          title: '${entry.value.value.toStringAsFixed(0)}%',
          color: colors[entry.key % colors.length],
          radius: 60,
        );
      }).toList(),
    );
  }

  /// بناء رسم بياني عمودي للتدفق النقدي
  BarChartData _buildCashFlowBarChart() {
    return BarChartData(
      gridData: const FlGridData(show: true),
      titlesData: const FlTitlesData(show: true),
      borderData: FlBorderData(show: true),
      barGroups: _dashboardData!.cashFlowChart.asMap().entries.map((entry) {
        return BarChartGroupData(
          x: entry.key,
          barRods: [
            BarChartRodData(
              toY: entry.value.value,
              color: entry.value.value >= 0
                  ? RevolutionaryColors.successGlow
                  : RevolutionaryColors.errorCoral,
              width: 16,
            ),
          ],
        );
      }).toList(),
    );
  }

  // دوال مساعدة

  /// الحصول على أيقونة الاتجاه
  IconData _getTrendIcon(KPITrend trend) {
    switch (trend) {
      case KPITrend.up:
        return Icons.trending_up;
      case KPITrend.down:
        return Icons.trending_down;
      case KPITrend.stable:
        return Icons.trending_flat;
    }
  }

  /// الحصول على لون الاتجاه
  Color _getTrendColor(KPITrend trend) {
    switch (trend) {
      case KPITrend.up:
        return RevolutionaryColors.successGlow;
      case KPITrend.down:
        return RevolutionaryColors.errorCoral;
      case KPITrend.stable:
        return Colors.grey;
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// تنسيق الوقت
  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  // دوال التفاعل

  /// عرض منتقي الفترة
  void _showPeriodSelector() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار الفترة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('آخر 7 أيام'),
              onTap: () {
                setState(() => _selectedPeriod = '7');
                Navigator.pop(context);
                _loadDashboardData();
              },
            ),
            ListTile(
              title: const Text('آخر 30 يوم'),
              onTap: () {
                setState(() => _selectedPeriod = '30');
                Navigator.pop(context);
                _loadDashboardData();
              },
            ),
            ListTile(
              title: const Text('آخر 90 يوم'),
              onTap: () {
                setState(() => _selectedPeriod = '90');
                Navigator.pop(context);
                _loadDashboardData();
              },
            ),
          ],
        ),
      ),
    );
  }

  /// عرض الإعدادات
  void _showSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إعدادات لوحة التحكم - قيد التطوير')),
    );
  }

  /// التنقل إلى فاتورة جديدة
  void _navigateToNewInvoice() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('فاتورة جديدة - قيد التطوير')));
  }

  /// التنقل إلى عميل جديد
  void _navigateToNewCustomer() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('عميل جديد - قيد التطوير')));
  }

  /// التنقل إلى التقارير
  void _navigateToReports() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('التقارير - قيد التطوير')));
  }

  /// إنشاء نسخة احتياطية
  void _createBackup() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('النسخ الاحتياطي - قيد التطوير')),
    );
  }
}

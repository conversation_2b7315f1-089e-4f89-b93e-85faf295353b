import 'package:flutter/material.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:excel/excel.dart';
import 'dart:io';
import '../constants/revolutionary_design_colors.dart';
import '../models/interactive_report_models.dart';
import '../models/report_models.dart';
import '../services/interactive_reports_service.dart';
import '../services/chart_service.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../services/report_settings_service.dart';
import '../constants/app_constants.dart';
import '../widgets/report_filter_widget.dart';
import '../widgets/interactive_chart_widget.dart';
import '../widgets/chart_customization_panel.dart';
import '../widgets/chart_statistics_widget.dart';

/// شاشة التقارير التفاعلية
class InteractiveReportScreen extends StatefulWidget {
  final String reportType;
  final String reportTitle;

  const InteractiveReportScreen({
    super.key,
    required this.reportType,
    required this.reportTitle,
  });

  @override
  State<InteractiveReportScreen> createState() =>
      _InteractiveReportScreenState();
}

class _InteractiveReportScreenState extends State<InteractiveReportScreen>
    with TickerProviderStateMixin {
  final InteractiveReportsService _reportsService = InteractiveReportsService();
  final ReportSettingsService _settingsService = ReportSettingsService();

  late TabController _mainTabController;
  InteractiveReportResult? _currentReport;
  ReportFilters _currentFilters = ReportFilters.empty();
  ReportConfiguration _currentConfig = ReportConfiguration.defaultConfig();

  bool _isLoading = false;
  bool _showFilters = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _mainTabController = TabController(length: 3, vsync: this);
    _initializeDefaultFilters();
    _loadReport();
  }

  @override
  void dispose() {
    _mainTabController.dispose();
    super.dispose();
  }

  void _initializeDefaultFilters() {
    final now = DateTime.now();
    final firstDayOfMonth = DateTime(now.year, now.month, 1);

    _currentFilters = ReportFilters.dateRange(firstDayOfMonth, now);
  }

  Future<void> _loadReport() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final report = await _reportsService.getInteractiveReport(
        reportType: widget.reportType,
        filters: _currentFilters,
        configuration: _currentConfig,
      );

      setState(() {
        _currentReport = report;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تحميل التقرير: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  void _onFiltersChanged(ReportFilters newFilters) {
    setState(() {
      _currentFilters = newFilters;
      _showFilters = false;
    });
    _loadReport();
  }

  void _onChartConfigurationChanged(ChartConfiguration newChartConfig) {
    // Update the chart settings in the report configuration
    final updatedConfig = _currentConfig.copyWith(
      chartSettings: {
        'showLegend': newChartConfig.showLegend,
        'showTooltips': newChartConfig.showTooltips,
        'showGrid': newChartConfig.showGrid,
        'primaryColor': newChartConfig.primaryColor?.toARGB32(),
        'secondaryColor': newChartConfig.secondaryColor?.toARGB32(),

        'height': newChartConfig.height,
        'width': newChartConfig.width,
        ...newChartConfig.customSettings,
      },
    );

    setState(() {
      _currentConfig = updatedConfig;
    });
    _loadReport();
  }

  ChartConfiguration _getChartConfiguration() {
    final chartSettings = _currentConfig.chartSettings;
    return ChartConfiguration(
      showLegend: chartSettings['showLegend'] ?? true,
      showTooltips: chartSettings['showTooltips'] ?? true,
      showGrid: chartSettings['showGrid'] ?? true,
      primaryColor: chartSettings['primaryColor'] != null
          ? Color(chartSettings['primaryColor'])
          : null,
      secondaryColor: chartSettings['secondaryColor'] != null
          ? Color(chartSettings['secondaryColor'])
          : null,
      height: chartSettings['height']?.toDouble(),
      width: chartSettings['width']?.toDouble(),
      customSettings: Map<String, dynamic>.from(
        chartSettings..removeWhere(
          (key, value) => [
            'showLegend',
            'showTooltips',
            'showGrid',
            'primaryColor',
            'secondaryColor',
            'height',
            'width',
          ].contains(key),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.reportTitle),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(
              _showFilters ? Icons.filter_list_off : Icons.filter_list,
            ),
            onPressed: () {
              setState(() {
                _showFilters = !_showFilters;
              });
            },
          ),
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadReport),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'export_pdf':
                  _exportToPDF();
                  break;
                case 'export_excel':
                  _exportToExcel();
                  break;
                case 'save_config':
                  _saveConfiguration();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export_pdf',
                child: Row(
                  children: [
                    Icon(Icons.picture_as_pdf),
                    SizedBox(width: 8),
                    Text('تصدير PDF'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export_excel',
                child: Row(
                  children: [
                    Icon(Icons.table_chart),
                    SizedBox(width: 8),
                    Text('تصدير Excel'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'save_config',
                child: Row(
                  children: [
                    Icon(Icons.save),
                    SizedBox(width: 8),
                    Text('حفظ الإعدادات'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Filters Panel
          if (_showFilters)
            Container(
              margin: const EdgeInsets.all(16),
              child: ReportFilterWidget(
                initialFilters: _currentFilters,
                onFiltersChanged: _onFiltersChanged,
                reportType: widget.reportType,
              ),
            ),

          // Report Content
          Expanded(child: _buildReportContent()),
        ],
      ),
    );
  }

  Widget _buildReportContent() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحميل التقرير...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: RevolutionaryColors.errorCoral,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: TextStyle(color: RevolutionaryColors.errorCoral),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadReport,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_currentReport == null) {
      return const Center(child: Text('لا توجد بيانات للعرض'));
    }

    return Column(
      children: [
        // Report Summary
        _buildReportSummary(),

        // Tabs
        TabBar(
          controller: _mainTabController,
          labelColor: RevolutionaryColors.damascusSky,
          unselectedLabelColor: Colors.grey,
          indicatorColor: RevolutionaryColors.damascusSky,
          tabs: const [
            Tab(text: 'الجدول', icon: Icon(Icons.table_chart)),
            Tab(text: 'الرسم البياني', icon: Icon(Icons.bar_chart)),
            Tab(text: 'الملخص', icon: Icon(Icons.summarize)),
          ],
        ),

        // Tab Content
        Expanded(
          child: TabBarView(
            controller: _mainTabController,
            children: [
              _buildTableView(),
              _buildChartView(),
              _buildSummaryView(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildReportSummary() {
    if (_currentReport == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: RevolutionaryColors.damascusSky.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _currentReport!.title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'عدد السجلات: ${_currentReport!.metadata.totalRecords}',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                'تم الإنشاء في:',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
              Text(
                '${_currentReport!.metadata.generatedAt.day}/${_currentReport!.metadata.generatedAt.month}/${_currentReport!.metadata.generatedAt.year}',
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTableView() {
    if (_currentReport == null) return const SizedBox.shrink();

    switch (widget.reportType) {
      case 'trial_balance':
        return _buildTrialBalanceTable();
      case 'profit_loss':
        return _buildProfitLossTable();
      case 'balance_sheet':
        return _buildBalanceSheetTable();
      case 'integration_status':
        return _buildIntegrationStatusTable();
      case 'advanced_inventory':
        return _buildAdvancedInventoryTable();
      case 'performance_analysis':
        return _buildPerformanceAnalysisTable();
      case 'audit_log':
        return _buildAuditLogTable();
      default:
        return const Center(
          child: Text('عرض الجدول غير متوفر لهذا النوع من التقارير'),
        );
    }
  }

  Widget _buildTrialBalanceTable() {
    final data = _currentReport!.data as List<TrialBalanceItem>;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: DataTable(
        columns: const [
          DataColumn(label: Text('رمز الحساب')),
          DataColumn(label: Text('اسم الحساب')),
          DataColumn(label: Text('مدين')),
          DataColumn(label: Text('دائن')),
          DataColumn(label: Text('الرصيد')),
        ],
        rows: data.map((item) {
          final balance =
              item.openingBalance + item.totalDebit - item.totalCredit;
          return DataRow(
            cells: [
              DataCell(Text(item.code)),
              DataCell(Text(item.name)),
              DataCell(Text(item.totalDebit.toStringAsFixed(2))),
              DataCell(Text(item.totalCredit.toStringAsFixed(2))),
              DataCell(
                Text(
                  balance.toStringAsFixed(2),
                  style: TextStyle(
                    color: balance >= 0
                        ? RevolutionaryColors.successGlow
                        : RevolutionaryColors.errorCoral,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildProfitLossTable() {
    final data = _currentReport!.data as ProfitLossReportData;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Revenue Section
          _buildSectionHeader('الإيرادات', RevolutionaryColors.successGlow),
          DataTable(
            columns: const [
              DataColumn(label: Text('رمز الحساب')),
              DataColumn(label: Text('اسم الحساب')),
              DataColumn(label: Text('المبلغ')),
            ],
            rows: data.revenueItems
                .map(
                  (item) => DataRow(
                    cells: [
                      DataCell(Text(item.accountCode)),
                      DataCell(Text(item.accountName)),
                      DataCell(Text(item.amount.toStringAsFixed(2))),
                    ],
                  ),
                )
                .toList(),
          ),

          const SizedBox(height: 20),

          // Expense Section
          _buildSectionHeader('المصروفات', RevolutionaryColors.errorCoral),
          DataTable(
            columns: const [
              DataColumn(label: Text('رمز الحساب')),
              DataColumn(label: Text('اسم الحساب')),
              DataColumn(label: Text('المبلغ')),
            ],
            rows: data.expenseItems
                .map(
                  (item) => DataRow(
                    cells: [
                      DataCell(Text(item.accountCode)),
                      DataCell(Text(item.accountName)),
                      DataCell(Text(item.amount.toStringAsFixed(2))),
                    ],
                  ),
                )
                .toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceSheetTable() {
    // Implementation for balance sheet table
    return const Center(child: Text('جدول الميزانية العمومية قيد التطوير'));
  }

  Widget _buildChartView() {
    if (_currentReport == null) {
      return const Center(child: Text('لا توجد بيانات للعرض'));
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: InteractiveChartWidget(
        reportType: widget.reportType,
        data: _currentReport!.data,
        initialChartType: ChartType.bar,
        configuration: _getChartConfiguration(),
        onChartTypeChanged: (chartType) {
          // يمكن إضافة منطق إضافي هنا عند تغيير نوع الرسم البياني
        },
      ),
    );
  }

  Widget _buildSummaryView() {
    if (_currentReport == null) {
      return const Center(child: Text('لا توجد بيانات للعرض'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Chart Statistics
          ChartStatisticsWidget(
            reportType: widget.reportType,
            data: _currentReport!.data,
          ),

          const SizedBox(height: 16),

          // Chart Customization Panel
          ChartCustomizationPanel(
            initialConfiguration: _getChartConfiguration(),
            onConfigurationChanged: _onChartConfigurationChanged,
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: color,
        ),
      ),
    );
  }

  void _exportToPDF() async {
    if (_currentReport == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('لا توجد بيانات للتصدير')));
      return;
    }

    try {
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      // إنشاء ملف PDF
      final pdf = await _generatePDF();

      // إخفاء مؤشر التحميل
      if (mounted) {
        Navigator.of(context).pop();
        // عرض خيارات التصدير
        _showPDFExportOptions(pdf);
      }
    } catch (e) {
      // إخفاء مؤشر التحميل في حالة الخطأ
      if (mounted) {
        Navigator.of(context).pop();
      }

      LoggingService.error(
        'خطأ في تصدير PDF',
        category: 'PDFExport',
        data: {'error': e.toString()},
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تصدير PDF: ${e.toString()}')),
        );
      }
    }
  }

  /// إنشاء ملف PDF
  Future<pw.Document> _generatePDF() async {
    final pdf = pw.Document();
    final report = _currentReport!;

    // إضافة صفحة للتقرير
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return [
            // عنوان التقرير
            _buildPDFHeader(report),
            pw.SizedBox(height: 20),

            // معلومات الفلاتر
            _buildPDFFilters(report.filters),
            pw.SizedBox(height: 20),

            // بيانات التقرير
            _buildPDFContent(report),
            pw.SizedBox(height: 20),

            // معلومات إضافية
            _buildPDFFooter(report),
          ];
        },
      ),
    );

    return pdf;
  }

  /// بناء رأس التقرير في PDF
  pw.Widget _buildPDFHeader(InteractiveReportResult report) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Smart Ledger - دفتر الأستاذ الذكي',
          style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        pw.Text(
          report.title,
          style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 5),
        pw.Text(
          'تاريخ الإنشاء: ${_formatDate(report.metadata.generatedAt)}',
          style: const pw.TextStyle(fontSize: 12),
        ),
        pw.Divider(),
      ],
    );
  }

  /// بناء فلاتر التقرير في PDF
  pw.Widget _buildPDFFilters(ReportFilters filters) {
    final filterItems = <String>[];

    if (filters.fromDate != null) {
      filterItems.add('من تاريخ: ${_formatDate(filters.fromDate!)}');
    }
    if (filters.toDate != null) {
      filterItems.add('إلى تاريخ: ${_formatDate(filters.toDate!)}');
    }
    if (filters.accountTypes.isNotEmpty) {
      filterItems.add('أنواع الحسابات: ${filters.accountTypes.join(', ')}');
    }
    if (filters.minAmount != null) {
      filterItems.add(
        'الحد الأدنى للمبلغ: ${filters.minAmount!.toStringAsFixed(2)}',
      );
    }
    if (filters.maxAmount != null) {
      filterItems.add(
        'الحد الأقصى للمبلغ: ${filters.maxAmount!.toStringAsFixed(2)}',
      );
    }

    if (filterItems.isEmpty) {
      return pw.Container();
    }

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'فلاتر التقرير:',
          style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 5),
        ...filterItems.map(
          (item) => pw.Padding(
            padding: const pw.EdgeInsets.only(left: 10, bottom: 2),
            child: pw.Text('• $item', style: const pw.TextStyle(fontSize: 12)),
          ),
        ),
        pw.Divider(),
      ],
    );
  }

  /// بناء محتوى التقرير في PDF
  pw.Widget _buildPDFContent(InteractiveReportResult report) {
    switch (report.reportType) {
      case 'trial_balance':
        return _buildTrialBalancePDF(report.data as List<TrialBalanceItem>);
      case 'profit_loss':
        return _buildProfitLossPDF(report.data as ProfitLossReportData);
      case 'balance_sheet':
        return _buildBalanceSheetPDF(report.data as BalanceSheetReportData);
      case 'inventory_report':
        return _buildInventoryReportPDF(
          report.data as List<InventoryReportItem>,
        );
      case 'sales_analysis':
        return _buildSalesAnalysisPDF(report.data as List<SalesAnalysisItem>);
      case 'purchase_analysis':
        return _buildPurchaseAnalysisPDF(
          report.data as List<PurchaseAnalysisItem>,
        );
      default:
        return pw.Text('نوع تقرير غير مدعوم: ${report.reportType}');
    }
  }

  /// بناء تذييل التقرير في PDF
  pw.Widget _buildPDFFooter(InteractiveReportResult report) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Divider(),
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Text(
              'إجمالي السجلات: ${report.metadata.totalRecords}',
              style: const pw.TextStyle(fontSize: 10),
            ),
            pw.Text(
              'وقت التنفيذ: ${report.metadata.executionTimeMs} مللي ثانية',
              style: const pw.TextStyle(fontSize: 10),
            ),
          ],
        ),
        pw.SizedBox(height: 5),
        pw.Text(
          'تم إنشاء هذا التقرير بواسطة Smart Ledger',
          style: const pw.TextStyle(fontSize: 10),
        ),
      ],
    );
  }

  Future<void> _exportToExcel() async {
    if (_currentReport == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('لا توجد بيانات للتصدير')));
      return;
    }

    try {
      // إنشاء ملف Excel جديد
      final excel = Excel.createExcel();
      final sheet = excel['التقرير'];

      // إزالة الورقة الافتراضية
      excel.delete('Sheet1');

      // إضافة عنوان التقرير
      sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue(
        _currentReport!.title,
      );
      sheet.cell(CellIndex.indexByString('A1')).cellStyle = CellStyle(
        bold: true,
        fontSize: 16,
        horizontalAlign: HorizontalAlign.Center,
      );

      // إضافة تاريخ التقرير
      final dateStr = 'تاريخ التقرير: ${_formatDate(DateTime.now())}';
      sheet.cell(CellIndex.indexByString('A2')).value = TextCellValue(dateStr);

      // إضافة البيانات حسب نوع التقرير
      switch (_currentReport!.reportType) {
        case 'trial_balance':
          await _exportTrialBalanceToExcel(
            sheet,
            _currentReport!.data as List<TrialBalanceItem>,
          );
          break;
        case 'profit_loss':
          await _exportProfitLossToExcel(
            sheet,
            _currentReport!.data as ProfitLossReportData,
          );
          break;
        case 'balance_sheet':
          await _exportBalanceSheetToExcel(
            sheet,
            _currentReport!.data as BalanceSheetReportData,
          );
          break;
        case 'inventory_report':
          await _exportInventoryReportToExcel(
            sheet,
            _currentReport!.data as List<InventoryReportItem>,
          );
          break;
        case 'sales_analysis':
          await _exportSalesAnalysisToExcel(
            sheet,
            _currentReport!.data as List<SalesAnalysisItem>,
          );
          break;
        case 'purchase_analysis':
          await _exportPurchaseAnalysisToExcel(
            sheet,
            _currentReport!.data as List<PurchaseAnalysisItem>,
          );
          break;
        default:
          throw Exception('نوع تقرير غير مدعوم: ${_currentReport!.reportType}');
      }

      // حفظ الملف
      final directory = await getApplicationDocumentsDirectory();
      final fileName =
          '${_currentReport!.title}_${DateTime.now().millisecondsSinceEpoch}.xlsx';
      final filePath = '${directory.path}/$fileName';

      final file = File(filePath);
      await file.writeAsBytes(excel.encode()!);

      // مشاركة الملف
      await Share.shareXFiles([
        XFile(filePath),
      ], text: 'تقرير ${_currentReport!.title}');

      // تسجيل العملية في السجل
      LoggingService.info(
        'تم تصدير تقرير ${_currentReport!.title} إلى Excel',
        category: 'تصدير تقرير Excel',
        data: {
          'reportType': _currentReport!.reportType,
          'fileName': fileName,
          'filePath': filePath,
        },
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'EXPORT_EXCEL',
        entityType: 'REPORT',
        entityName: _currentReport!.title,
        description: 'تصدير تقرير Excel',
        severity: 'INFO',
        category: 'Reports',
        newValues: {
          'reportType': _currentReport!.reportType,
          'reportTitle': _currentReport!.title,
          'fileName': fileName,
        },
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم تصدير التقرير بنجاح: $fileName')),
        );
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في تصدير Excel',
        category: 'Reports',
        data: {'error': e.toString()},
      );
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تصدير التقرير: $e')));
      }
    }
  }

  /// تصدير ميزان المراجعة إلى Excel
  Future<void> _exportTrialBalanceToExcel(
    Sheet sheet,
    List<TrialBalanceItem> data,
  ) async {
    // إضافة رؤوس الأعمدة
    final headers = ['رمز الحساب', 'اسم الحساب', 'مدين', 'دائن', 'الرصيد'];
    for (int i = 0; i < headers.length; i++) {
      final cell = sheet.cell(
        CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 3),
      );
      cell.value = TextCellValue(headers[i]);
      cell.cellStyle = CellStyle(
        bold: true,
        backgroundColorHex: ExcelColor.fromHexString('#E0E0E0'),
        horizontalAlign: HorizontalAlign.Center,
      );
    }

    // إضافة البيانات
    for (int i = 0; i < data.length; i++) {
      final item = data[i];
      final balance = item.openingBalance + item.totalDebit - item.totalCredit;
      final rowIndex = i + 4;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: rowIndex))
          .value = TextCellValue(
        item.code,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: rowIndex))
          .value = TextCellValue(
        item.name,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: rowIndex))
          .value = DoubleCellValue(
        item.totalDebit,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: rowIndex))
          .value = DoubleCellValue(
        item.totalCredit,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: rowIndex))
          .value = DoubleCellValue(
        balance,
      );
    }

    // إضافة المجاميع
    final totalRow = data.length + 4;
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: totalRow))
        .value = TextCellValue(
      'المجموع',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: totalRow))
        .cellStyle = CellStyle(
      bold: true,
    );

    final totalDebit = data.fold(0.0, (sum, item) => sum + item.totalDebit);
    final totalCredit = data.fold(0.0, (sum, item) => sum + item.totalCredit);

    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: totalRow))
        .value = DoubleCellValue(
      totalDebit,
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: totalRow))
        .value = DoubleCellValue(
      totalCredit,
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: totalRow))
        .cellStyle = CellStyle(
      bold: true,
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: totalRow))
        .cellStyle = CellStyle(
      bold: true,
    );
  }

  /// تصدير قائمة الدخل إلى Excel
  Future<void> _exportProfitLossToExcel(
    Sheet sheet,
    ProfitLossReportData data,
  ) async {
    int currentRow = 4;

    // الإيرادات
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .value = TextCellValue(
      'الإيرادات',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .cellStyle = CellStyle(
      bold: true,
      backgroundColorHex: ExcelColor.fromHexString('#0066CC'),
    );
    currentRow++;

    for (final item in data.revenueItems) {
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
          )
          .value = TextCellValue(
        item.accountName,
      );
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow),
          )
          .value = DoubleCellValue(
        item.amount,
      );
      currentRow++;
    }

    // إجمالي الإيرادات
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .value = TextCellValue(
      'إجمالي الإيرادات',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow))
        .value = DoubleCellValue(
      data.totalRevenue,
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .cellStyle = CellStyle(
      bold: true,
    );
    currentRow += 2;

    // المصروفات
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .value = TextCellValue(
      'المصروفات',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .cellStyle = CellStyle(
      bold: true,
      backgroundColorHex: ExcelColor.fromHexString('#CC0000'),
    );
    currentRow++;

    for (final item in data.expenseItems) {
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
          )
          .value = TextCellValue(
        item.accountName,
      );
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow),
          )
          .value = DoubleCellValue(
        item.amount,
      );
      currentRow++;
    }

    // إجمالي المصروفات
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .value = TextCellValue(
      'إجمالي المصروفات',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow))
        .value = DoubleCellValue(
      data.totalExpense,
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .cellStyle = CellStyle(
      bold: true,
    );
    currentRow += 2;

    // صافي الربح/الخسارة
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .value = TextCellValue(
      'صافي الربح/الخسارة',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow))
        .value = DoubleCellValue(
      data.netProfit,
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .cellStyle = CellStyle(
      bold: true,
      backgroundColorHex: ExcelColor.fromHexString('#00CC00'),
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow))
        .cellStyle = CellStyle(
      bold: true,
    );
  }

  /// تصدير الميزانية العمومية إلى Excel
  Future<void> _exportBalanceSheetToExcel(
    Sheet sheet,
    BalanceSheetReportData data,
  ) async {
    int currentRow = 4;

    // الأصول
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .value = TextCellValue(
      'الأصول',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .cellStyle = CellStyle(
      bold: true,
      backgroundColorHex: ExcelColor.fromHexString('#0066CC'),
    );
    currentRow++;

    for (final item in data.assets) {
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
          )
          .value = TextCellValue(
        item.accountName,
      );
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow),
          )
          .value = DoubleCellValue(
        item.balance,
      );
      currentRow++;
    }

    // إجمالي الأصول
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .value = TextCellValue(
      'إجمالي الأصول',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow))
        .value = DoubleCellValue(
      data.totalAssets,
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .cellStyle = CellStyle(
      bold: true,
    );
    currentRow += 2;

    // الخصوم
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .value = TextCellValue(
      'الخصوم',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .cellStyle = CellStyle(
      bold: true,
      backgroundColorHex: ExcelColor.fromHexString('#CC0000'),
    );
    currentRow++;

    for (final item in data.liabilities) {
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
          )
          .value = TextCellValue(
        item.accountName,
      );
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow),
          )
          .value = DoubleCellValue(
        item.balance,
      );
      currentRow++;
    }

    // إجمالي الخصوم
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .value = TextCellValue(
      'إجمالي الخصوم',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow))
        .value = DoubleCellValue(
      data.totalLiabilities,
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .cellStyle = CellStyle(
      bold: true,
    );
    currentRow += 2;

    // حقوق الملكية
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .value = TextCellValue(
      'حقوق الملكية',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .cellStyle = CellStyle(
      bold: true,
      backgroundColorHex: ExcelColor.fromHexString('#00CC00'),
    );
    currentRow++;

    for (final item in data.equity) {
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
          )
          .value = TextCellValue(
        item.accountName,
      );
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow),
          )
          .value = DoubleCellValue(
        item.balance,
      );
      currentRow++;
    }

    // إجمالي حقوق الملكية
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .value = TextCellValue(
      'إجمالي حقوق الملكية',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow))
        .value = DoubleCellValue(
      data.totalEquity,
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .cellStyle = CellStyle(
      bold: true,
    );
  }

  /// تصدير تقرير المخزون إلى Excel
  Future<void> _exportInventoryReportToExcel(
    Sheet sheet,
    List<InventoryReportItem> data,
  ) async {
    // إضافة رؤوس الأعمدة
    final headers = [
      'رمز الصنف',
      'اسم الصنف',
      'الوحدة',
      'الكمية',
      'الحد الأدنى',
      'سعر التكلفة',
      'سعر البيع',
      'قيمة التكلفة',
      'قيمة البيع',
      'حالة المخزون',
    ];
    for (int i = 0; i < headers.length; i++) {
      final cell = sheet.cell(
        CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 3),
      );
      cell.value = TextCellValue(headers[i]);
      cell.cellStyle = CellStyle(
        bold: true,
        backgroundColorHex: ExcelColor.fromHexString('#E0E0E0'),
        horizontalAlign: HorizontalAlign.Center,
      );
    }

    // إضافة البيانات
    for (int i = 0; i < data.length; i++) {
      final item = data[i];
      final rowIndex = i + 4;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: rowIndex))
          .value = TextCellValue(
        item.code,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: rowIndex))
          .value = TextCellValue(
        item.name,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: rowIndex))
          .value = TextCellValue(
        item.unit,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: rowIndex))
          .value = DoubleCellValue(
        item.quantity,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: rowIndex))
          .value = DoubleCellValue(
        item.minQuantity,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: rowIndex))
          .value = DoubleCellValue(
        item.costPrice,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: rowIndex))
          .value = DoubleCellValue(
        item.sellingPrice,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 7, rowIndex: rowIndex))
          .value = DoubleCellValue(
        item.totalCostValue,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 8, rowIndex: rowIndex))
          .value = DoubleCellValue(
        item.totalSellingValue,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 9, rowIndex: rowIndex))
          .value = TextCellValue(
        item.stockStatus,
      );
    }

    // إضافة المجاميع
    final totalRow = data.length + 4;
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: totalRow))
        .value = TextCellValue(
      'المجموع',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: totalRow))
        .cellStyle = CellStyle(
      bold: true,
    );

    final totalCostValue = data.fold(
      0.0,
      (sum, item) => sum + item.totalCostValue,
    );
    final totalSellingValue = data.fold(
      0.0,
      (sum, item) => sum + item.totalSellingValue,
    );

    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 7, rowIndex: totalRow))
        .value = DoubleCellValue(
      totalCostValue,
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 8, rowIndex: totalRow))
        .value = DoubleCellValue(
      totalSellingValue,
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 7, rowIndex: totalRow))
        .cellStyle = CellStyle(
      bold: true,
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 8, rowIndex: totalRow))
        .cellStyle = CellStyle(
      bold: true,
    );
  }

  /// تصدير تحليل المبيعات إلى Excel
  Future<void> _exportSalesAnalysisToExcel(
    Sheet sheet,
    List<SalesAnalysisItem> data,
  ) async {
    // إضافة رؤوس الأعمدة
    final headers = [
      'التاريخ',
      'عدد الفواتير',
      'إجمالي المبيعات',
      'متوسط الفاتورة',
      'إجمالي الضريبة',
    ];
    for (int i = 0; i < headers.length; i++) {
      final cell = sheet.cell(
        CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 3),
      );
      cell.value = TextCellValue(headers[i]);
      cell.cellStyle = CellStyle(
        bold: true,
        backgroundColorHex: ExcelColor.fromHexString('#E0E0E0'),
        horizontalAlign: HorizontalAlign.Center,
      );
    }

    // إضافة البيانات
    for (int i = 0; i < data.length; i++) {
      final item = data[i];
      final rowIndex = i + 4;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: rowIndex))
          .value = TextCellValue(
        item.saleDate,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: rowIndex))
          .value = IntCellValue(
        item.invoiceCount,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: rowIndex))
          .value = DoubleCellValue(
        item.totalSales,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: rowIndex))
          .value = DoubleCellValue(
        item.averageInvoice,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: rowIndex))
          .value = DoubleCellValue(
        item.totalTax,
      );
    }

    // إضافة المجاميع
    final totalRow = data.length + 4;
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: totalRow))
        .value = TextCellValue(
      'المجموع',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: totalRow))
        .cellStyle = CellStyle(
      bold: true,
    );

    final totalInvoices = data.fold(0, (sum, item) => sum + item.invoiceCount);
    final totalSales = data.fold(0.0, (sum, item) => sum + item.totalSales);
    final totalTax = data.fold(0.0, (sum, item) => sum + item.totalTax);

    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: totalRow))
        .value = IntCellValue(
      totalInvoices,
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: totalRow))
        .value = DoubleCellValue(
      totalSales,
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: totalRow))
        .value = DoubleCellValue(
      totalTax,
    );

    // تنسيق المجاميع
    for (int i = 1; i <= 4; i++) {
      if (i != 3) {
        // تجاهل عمود متوسط الفاتورة
        sheet
            .cell(
              CellIndex.indexByColumnRow(columnIndex: i, rowIndex: totalRow),
            )
            .cellStyle = CellStyle(
          bold: true,
        );
      }
    }
  }

  /// تصدير تحليل المشتريات إلى Excel
  Future<void> _exportPurchaseAnalysisToExcel(
    Sheet sheet,
    List<PurchaseAnalysisItem> data,
  ) async {
    // إضافة رؤوس الأعمدة
    final headers = [
      'التاريخ',
      'عدد الفواتير',
      'إجمالي المشتريات',
      'متوسط الفاتورة',
      'إجمالي الضريبة',
    ];
    for (int i = 0; i < headers.length; i++) {
      final cell = sheet.cell(
        CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 3),
      );
      cell.value = TextCellValue(headers[i]);
      cell.cellStyle = CellStyle(
        bold: true,
        backgroundColorHex: ExcelColor.fromHexString('#E0E0E0'),
        horizontalAlign: HorizontalAlign.Center,
      );
    }

    // إضافة البيانات
    for (int i = 0; i < data.length; i++) {
      final item = data[i];
      final rowIndex = i + 4;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: rowIndex))
          .value = TextCellValue(
        item.purchaseDate,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: rowIndex))
          .value = IntCellValue(
        item.invoiceCount,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: rowIndex))
          .value = DoubleCellValue(
        item.totalPurchases,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: rowIndex))
          .value = DoubleCellValue(
        item.averageInvoice,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: rowIndex))
          .value = DoubleCellValue(
        item.totalTax,
      );
    }

    // إضافة المجاميع
    final totalRow = data.length + 4;
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: totalRow))
        .value = TextCellValue(
      'المجموع',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: totalRow))
        .cellStyle = CellStyle(
      bold: true,
    );

    final totalInvoices = data.fold(0, (sum, item) => sum + item.invoiceCount);
    final totalPurchases = data.fold(
      0.0,
      (sum, item) => sum + item.totalPurchases,
    );
    final totalTax = data.fold(0.0, (sum, item) => sum + item.totalTax);

    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: totalRow))
        .value = IntCellValue(
      totalInvoices,
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: totalRow))
        .value = DoubleCellValue(
      totalPurchases,
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: totalRow))
        .value = DoubleCellValue(
      totalTax,
    );

    // تنسيق المجاميع
    for (int i = 1; i <= 4; i++) {
      if (i != 3) {
        // تجاهل عمود متوسط الفاتورة
        sheet
            .cell(
              CellIndex.indexByColumnRow(columnIndex: i, rowIndex: totalRow),
            )
            .cellStyle = CellStyle(
          bold: true,
        );
      }
    }
  }

  /// تنسيق التاريخ للعرض
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// بناء جدول ميزان المراجعة في PDF
  pw.Widget _buildTrialBalancePDF(List<TrialBalanceItem> data) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'ميزان المراجعة',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(),
          children: [
            // رأس الجدول
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey300),
              children: [
                _buildTableCell('رمز الحساب', isHeader: true),
                _buildTableCell('اسم الحساب', isHeader: true),
                _buildTableCell('مدين', isHeader: true),
                _buildTableCell('دائن', isHeader: true),
                _buildTableCell('الرصيد', isHeader: true),
              ],
            ),
            // بيانات الجدول
            ...data.map((item) {
              final balance =
                  item.openingBalance + item.totalDebit - item.totalCredit;
              return pw.TableRow(
                children: [
                  _buildTableCell(item.code),
                  _buildTableCell(item.name),
                  _buildTableCell(item.totalDebit.toStringAsFixed(2)),
                  _buildTableCell(item.totalCredit.toStringAsFixed(2)),
                  _buildTableCell(balance.toStringAsFixed(2)),
                ],
              );
            }),
          ],
        ),
      ],
    );
  }

  /// بناء تقرير قائمة الدخل في PDF
  pw.Widget _buildProfitLossPDF(ProfitLossReportData data) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'قائمة الدخل',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        pw.Text(
          'الفترة: ${_formatDate(data.fromDate)} - ${_formatDate(data.toDate)}',
        ),
        pw.SizedBox(height: 10),

        // الإيرادات
        pw.Text(
          'الإيرادات:',
          style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
        ),
        ...data.revenueItems.map(
          (item) => pw.Padding(
            padding: const pw.EdgeInsets.only(left: 20, bottom: 2),
            child: pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text(item.accountName),
                pw.Text(item.amount.toStringAsFixed(2)),
              ],
            ),
          ),
        ),
        pw.Divider(),
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Text(
              'إجمالي الإيرادات:',
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
            pw.Text(
              data.totalRevenue.toStringAsFixed(2),
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
          ],
        ),
        pw.SizedBox(height: 10),

        // المصروفات
        pw.Text(
          'المصروفات:',
          style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
        ),
        ...data.expenseItems.map(
          (item) => pw.Padding(
            padding: const pw.EdgeInsets.only(left: 20, bottom: 2),
            child: pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text(item.accountName),
                pw.Text(item.amount.toStringAsFixed(2)),
              ],
            ),
          ),
        ),
        pw.Divider(),
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Text(
              'إجمالي المصروفات:',
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
            pw.Text(
              data.totalExpense.toStringAsFixed(2),
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
          ],
        ),
        pw.SizedBox(height: 10),

        // صافي الربح
        pw.Divider(),
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Text(
              'صافي الربح/الخسارة:',
              style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
            ),
            pw.Text(
              data.netProfit.toStringAsFixed(2),
              style: pw.TextStyle(
                fontSize: 16,
                fontWeight: pw.FontWeight.bold,
                color: data.netProfit >= 0 ? PdfColors.green : PdfColors.red,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء خلية جدول
  pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Padding(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 12 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// بناء تقرير الميزانية العمومية في PDF
  pw.Widget _buildBalanceSheetPDF(BalanceSheetReportData data) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'الميزانية العمومية',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        pw.Text('كما في: ${_formatDate(data.asOfDate)}'),
        pw.SizedBox(height: 10),

        // الأصول
        pw.Text(
          'الأصول:',
          style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
        ),
        ...data.assets.map(
          (item) => pw.Padding(
            padding: const pw.EdgeInsets.only(left: 20, bottom: 2),
            child: pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text(item.accountName),
                pw.Text(item.balance.toStringAsFixed(2)),
              ],
            ),
          ),
        ),
        pw.Divider(),
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Text(
              'إجمالي الأصول:',
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
            pw.Text(
              data.totalAssets.toStringAsFixed(2),
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
          ],
        ),
        pw.SizedBox(height: 10),

        // الخصوم
        pw.Text(
          'الخصوم:',
          style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
        ),
        ...data.liabilities.map(
          (item) => pw.Padding(
            padding: const pw.EdgeInsets.only(left: 20, bottom: 2),
            child: pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text(item.accountName),
                pw.Text(item.balance.toStringAsFixed(2)),
              ],
            ),
          ),
        ),
        pw.Divider(),
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Text(
              'إجمالي الخصوم:',
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
            pw.Text(
              data.totalLiabilities.toStringAsFixed(2),
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
          ],
        ),
        pw.SizedBox(height: 10),

        // حقوق الملكية
        pw.Text(
          'حقوق الملكية:',
          style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
        ),
        ...data.equity.map(
          (item) => pw.Padding(
            padding: const pw.EdgeInsets.only(left: 20, bottom: 2),
            child: pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text(item.accountName),
                pw.Text(item.balance.toStringAsFixed(2)),
              ],
            ),
          ),
        ),
        pw.Divider(),
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Text(
              'إجمالي حقوق الملكية:',
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
            pw.Text(
              data.totalEquity.toStringAsFixed(2),
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء تقرير المخزون في PDF
  pw.Widget _buildInventoryReportPDF(List<InventoryReportItem> data) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'تقرير المخزون',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(),
          children: [
            // رأس الجدول
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey300),
              children: [
                _buildTableCell('رمز الصنف', isHeader: true),
                _buildTableCell('اسم الصنف', isHeader: true),
                _buildTableCell('الكمية', isHeader: true),
                _buildTableCell('سعر التكلفة', isHeader: true),
                _buildTableCell('القيمة الإجمالية', isHeader: true),
              ],
            ),
            // بيانات الجدول
            ...data.map(
              (item) => pw.TableRow(
                children: [
                  _buildTableCell(item.code),
                  _buildTableCell(item.name),
                  _buildTableCell(item.quantity.toString()),
                  _buildTableCell(item.costPrice.toStringAsFixed(2)),
                  _buildTableCell(item.totalCostValue.toStringAsFixed(2)),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء تقرير تحليل المبيعات في PDF
  pw.Widget _buildSalesAnalysisPDF(List<SalesAnalysisItem> data) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'تحليل المبيعات',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(),
          children: [
            // رأس الجدول
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey300),
              children: [
                _buildTableCell('التاريخ', isHeader: true),
                _buildTableCell('عدد الفواتير', isHeader: true),
                _buildTableCell('إجمالي المبيعات', isHeader: true),
                _buildTableCell('متوسط الفاتورة', isHeader: true),
              ],
            ),
            // بيانات الجدول
            ...data.map(
              (item) => pw.TableRow(
                children: [
                  _buildTableCell(item.saleDate),
                  _buildTableCell(item.invoiceCount.toString()),
                  _buildTableCell(item.totalSales.toStringAsFixed(2)),
                  _buildTableCell(item.averageInvoice.toStringAsFixed(2)),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء تقرير تحليل المشتريات في PDF
  pw.Widget _buildPurchaseAnalysisPDF(List<PurchaseAnalysisItem> data) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'تحليل المشتريات',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(),
          children: [
            // رأس الجدول
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey300),
              children: [
                _buildTableCell('التاريخ', isHeader: true),
                _buildTableCell('عدد الفواتير', isHeader: true),
                _buildTableCell('إجمالي المشتريات', isHeader: true),
                _buildTableCell('متوسط الفاتورة', isHeader: true),
              ],
            ),
            // بيانات الجدول
            ...data.map(
              (item) => pw.TableRow(
                children: [
                  _buildTableCell(item.purchaseDate),
                  _buildTableCell(item.invoiceCount.toString()),
                  _buildTableCell(item.totalPurchases.toStringAsFixed(2)),
                  _buildTableCell(item.averageInvoice.toStringAsFixed(2)),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// عرض خيارات تصدير PDF
  void _showPDFExportOptions(pw.Document pdf) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصدير PDF'),
        content: const Text('اختر طريقة التصدير:'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _previewPDF(pdf);
            },
            child: const Text('معاينة'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _savePDFToFile(pdf);
            },
            child: const Text('حفظ'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _sharePDF(pdf);
            },
            child: const Text('مشاركة'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  /// معاينة PDF
  Future<void> _previewPDF(pw.Document pdf) async {
    try {
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
        name: '${_currentReport!.title}.pdf',
      );

      // تسجيل العملية
      await AuditService.logCreate(
        entityType: AppConstants.auditEntitySystem,
        entityId: 0,
        entityName: 'معاينة تقرير PDF',
        newValues: {
          'reportType': _currentReport!.reportType,
          'reportTitle': _currentReport!.title,
        },
        description: 'تم معاينة تقرير PDF',
        category: 'PDFExport',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في معاينة PDF',
        category: 'PDFExport',
        data: {'error': e.toString()},
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في معاينة PDF: ${e.toString()}')),
        );
      }
    }
  }

  /// حفظ PDF إلى ملف
  Future<void> _savePDFToFile(pw.Document pdf) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName =
          '${_currentReport!.title}_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final file = File('${directory.path}/$fileName');

      await file.writeAsBytes(await pdf.save());

      // تسجيل العملية
      await AuditService.logCreate(
        entityType: AppConstants.auditEntitySystem,
        entityId: 0,
        entityName: 'حفظ تقرير PDF',
        newValues: {
          'reportType': _currentReport!.reportType,
          'reportTitle': _currentReport!.title,
          'fileName': fileName,
          'filePath': file.path,
        },
        description: 'تم حفظ تقرير PDF',
        category: 'PDFExport',
      );

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('تم حفظ الملف: $fileName')));
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في حفظ PDF',
        category: 'PDFExport',
        data: {'error': e.toString()},
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في حفظ PDF: ${e.toString()}')),
        );
      }
    }
  }

  /// مشاركة PDF
  Future<void> _sharePDF(pw.Document pdf) async {
    try {
      final directory = await getTemporaryDirectory();
      final fileName =
          '${_currentReport!.title}_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final file = File('${directory.path}/$fileName');

      await file.writeAsBytes(await pdf.save());

      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'تقرير من Smart Ledger: ${_currentReport!.title}',
        subject: _currentReport!.title,
      );

      // تسجيل العملية
      await AuditService.logCreate(
        entityType: AppConstants.auditEntitySystem,
        entityId: 0,
        entityName: 'مشاركة تقرير PDF',
        newValues: {
          'reportType': _currentReport!.reportType,
          'reportTitle': _currentReport!.title,
          'fileName': fileName,
        },
        description: 'تم مشاركة تقرير PDF',
        category: 'PDFExport',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في مشاركة PDF',
        category: 'PDFExport',
        data: {'error': e.toString()},
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في مشاركة PDF: ${e.toString()}')),
        );
      }
    }
  }

  void _saveConfiguration() {
    _showSaveConfigurationDialog();
  }

  /// عرض حوار حفظ الإعدادات
  void _showSaveConfigurationDialog() {
    final TextEditingController nameController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'حفظ إعدادات التقرير',
            style: TextStyle(fontFamily: 'Cairo', fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'أدخل اسماً لحفظ إعدادات التقرير الحالية',
                style: TextStyle(fontFamily: 'Cairo'),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'اسم الإعدادات',
                  hintText: 'مثال: إعدادات شهرية',
                  border: OutlineInputBorder(),
                ),
                style: const TextStyle(fontFamily: 'Cairo'),
                autofocus: true,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء', style: TextStyle(fontFamily: 'Cairo')),
            ),
            ElevatedButton(
              onPressed: () {
                final configName = nameController.text.trim();
                if (configName.isNotEmpty) {
                  Navigator.of(context).pop();
                  _performSaveConfiguration(configName);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: RevolutionaryColors.damascusSky,
                foregroundColor: Colors.white,
              ),
              child: const Text('حفظ', style: TextStyle(fontFamily: 'Cairo')),
            ),
          ],
        );
      },
    );
  }

  /// تنفيذ حفظ الإعدادات
  Future<void> _performSaveConfiguration(String configName) async {
    try {
      // حفظ الإعدادات الافتراضية للتقرير
      final defaultSaved = await _settingsService.saveReportSettings(
        reportType: widget.reportType,
        filters: _currentFilters,
        configuration: _currentConfig,
        configName: 'الإعدادات الافتراضية',
      );

      // حفظ الإعدادات المخصصة بالاسم المحدد
      final namedSaved = await _settingsService.saveNamedConfiguration(
        reportType: widget.reportType,
        configName: configName,
        filters: _currentFilters,
        configuration: _currentConfig,
      );

      if (defaultSaved && namedSaved) {
        // تسجيل العملية في السجل
        LoggingService.info(
          'تم حفظ إعدادات التقرير',
          category: 'ReportConfiguration',
          data: {
            'reportType': widget.reportType,
            'configName': configName,
            'filtersCount': _getFiltersCount(),
          },
        );

        // تسجيل في سجل المراجعة
        AuditService.log(
          action: 'SAVE_REPORT_CONFIG',
          entityType: 'ReportConfiguration',
          entityName: configName,
          description: 'حفظ إعدادات تقرير: $configName',
          category: 'ReportConfiguration',
          newValues: {
            'reportType': widget.reportType,
            'configName': configName,
            'filtersCount': _getFiltersCount(),
          },
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حفظ إعدادات التقرير "$configName" بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في حفظ إعدادات التقرير'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في حفظ إعدادات التقرير',
        category: 'ReportConfiguration',
        data: {
          'reportType': widget.reportType,
          'configName': configName,
          'error': e.toString(),
        },
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الإعدادات: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// حساب عدد الفلاتر المطبقة
  int _getFiltersCount() {
    int count = 0;
    if (_currentFilters.fromDate != null) count++;
    if (_currentFilters.toDate != null) count++;
    if (_currentFilters.accountTypes.isNotEmpty) count++;
    if (_currentFilters.accountCodes.isNotEmpty) count++;
    if (_currentFilters.accountIds.isNotEmpty) count++;
    if (_currentFilters.customerIds.isNotEmpty) count++;
    if (_currentFilters.supplierIds.isNotEmpty) count++;
    if (_currentFilters.itemIds.isNotEmpty) count++;
    if (_currentFilters.minAmount != null) count++;
    if (_currentFilters.maxAmount != null) count++;
    if (_currentFilters.searchText != null &&
        _currentFilters.searchText!.isNotEmpty) {
      count++;
    }
    if (_currentFilters.customFilters.isNotEmpty) count++;
    return count;
  }

  /// بناء جدول تقرير حالة التكامل
  Widget _buildIntegrationStatusTable() {
    final data = _currentReport!.data as Map<String, dynamic>;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'ملخص التكامل',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 16),
                  _buildIntegrationSummaryRow(
                    'إجمالي الفواتير',
                    '${data['totalInvoices'] ?? 0}',
                  ),
                  _buildIntegrationSummaryRow(
                    'الفواتير المتكاملة',
                    '${data['integratedInvoices'] ?? 0}',
                  ),
                  _buildIntegrationSummaryRow(
                    'نسبة التكامل',
                    '${(data['integrationPercentage'] ?? 0).toStringAsFixed(1)}%',
                  ),
                  _buildIntegrationSummaryRow(
                    'القيود المحاسبية المفقودة',
                    '${data['missingJournalEntries'] ?? 0}',
                  ),
                  _buildIntegrationSummaryRow(
                    'حركات المخزون المفقودة',
                    '${data['missingInventoryMovements'] ?? 0}',
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          if (data['issues'] != null &&
              (data['issues'] as List).isNotEmpty) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المشاكل المكتشفة',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 16),
                    ...(data['issues'] as List).map(
                      (issue) => ListTile(
                        leading: const Icon(
                          Icons.warning,
                          color: Colors.orange,
                        ),
                        title: Text('فاتورة رقم: ${issue['invoiceNumber']}'),
                        subtitle: Text(_getIssueDescription(issue['issue'])),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildIntegrationSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  String _getIssueDescription(String issue) {
    switch (issue) {
      case 'missing_journal_entry':
        return 'قيد محاسبي مفقود';
      case 'missing_inventory_movement':
        return 'حركة مخزون مفقودة';
      default:
        return issue;
    }
  }

  /// بناء جدول تقرير المخزون المتقدم
  Widget _buildAdvancedInventoryTable() {
    final data = _currentReport!.data as List<InventoryReportItem>;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: DataTable(
        columns: const [
          DataColumn(label: Text('الكود')),
          DataColumn(label: Text('الاسم')),
          DataColumn(label: Text('الكمية')),
          DataColumn(label: Text('الحالة')),
          DataColumn(label: Text('القيمة')),
        ],
        rows: data
            .map(
              (item) => DataRow(
                cells: [
                  DataCell(Text(item.code)),
                  DataCell(Text(item.name)),
                  DataCell(Text('${item.quantity} ${item.unit}')),
                  DataCell(Text(item.stockStatus)),
                  DataCell(
                    Text('${item.totalCostValue.toStringAsFixed(2)} ل.س'),
                  ),
                ],
              ),
            )
            .toList(),
      ),
    );
  }

  /// بناء جدول تقرير تحليل الأداء
  Widget _buildPerformanceAnalysisTable() {
    final data = _currentReport!.data as Map<String, dynamic>;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'إحصائيات قاعدة البيانات',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 16),
                  ...((data['databaseStats'] as Map<String, dynamic>? ?? {})
                      .entries
                      .map(
                        (entry) => _buildPerformanceRow(
                          entry.key,
                          entry.value.toString(),
                        ),
                      )),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'إحصائيات العمليات',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 16),
                  ...((data['operationStats'] as Map<String, dynamic>? ?? {})
                      .entries
                      .map(
                        (entry) => _buildPerformanceRow(
                          entry.key,
                          entry.value.toString(),
                        ),
                      )),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  /// بناء جدول سجل المراجعة
  Widget _buildAuditLogTable() {
    final data = _currentReport!.data as List;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: DataTable(
        columns: const [
          DataColumn(label: Text('التاريخ')),
          DataColumn(label: Text('العملية')),
          DataColumn(label: Text('النوع')),
          DataColumn(label: Text('الوصف')),
          DataColumn(label: Text('المستخدم')),
        ],
        rows: data
            .map(
              (log) => DataRow(
                cells: [
                  DataCell(Text(log.timestamp?.toString().split(' ')[0] ?? '')),
                  DataCell(Text(log.action ?? '')),
                  DataCell(Text(log.entityType ?? '')),
                  DataCell(Text(log.description ?? '')),
                  DataCell(Text(log.userName ?? '')),
                ],
              ),
            )
            .toList(),
      ),
    );
  }
}

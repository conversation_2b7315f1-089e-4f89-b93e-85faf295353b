import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/interactive_report_models.dart';
import '../models/report_models.dart';
import '../constants/revolutionary_design_colors.dart';
import 'logging_service.dart';

/// خدمة إنشاء وإدارة الرسوم البيانية
class ChartService {
  /// إنشاء رسم بياني للتقرير
  static Widget createChart({
    required String reportType,
    required dynamic data,
    required ChartType chartType,
    ChartConfiguration? configuration,
  }) {
    try {
      LoggingService.info(
        'إنشاء رسم بياني',
        category: 'ChartService',
        data: {'reportType': reportType, 'chartType': chartType.toString()},
      );

      switch (reportType) {
        case 'trial_balance':
          return _createTrialBalanceChart(data, chartType, configuration);
        case 'profit_loss':
          return _createProfitLoss<PERSON>hart(data, chartType, configuration);
        case 'balance_sheet':
          return _createBalanceSheet<PERSON>hart(data, chartType, configuration);
        case 'customer_aging':
          return _createCustomerAgingChart(data, chartType, configuration);
        case 'sales_analysis':
          return _createSalesAnalysisChart(data, chartType, configuration);
        case 'purchase_analysis':
          return _createPurchaseAnalysisChart(data, chartType, configuration);
        case 'inventory_report':
          return _createInventoryChart(data, chartType, configuration);
        default:
          return _createDefaultChart();
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء الرسم البياني',
        category: 'ChartService',
        data: {
          'reportType': reportType,
          'chartType': chartType.toString(),
          'error': e.toString(),
        },
      );
      return _createErrorChart(e.toString());
    }
  }

  /// رسم بياني لميزان المراجعة
  static Widget _createTrialBalanceChart(
    List<TrialBalanceItem> data,
    ChartType chartType,
    ChartConfiguration? config,
  ) {
    switch (chartType) {
      case ChartType.bar:
        return _createTrialBalanceBarChart(data, config);
      case ChartType.pie:
        return _createTrialBalancePieChart(data, config);
      case ChartType.line:
        return _createTrialBalanceLineChart(data, config);
      default:
        return _createTrialBalanceBarChart(data, config);
    }
  }

  /// رسم بياني أعمدة لميزان المراجعة
  static Widget _createTrialBalanceBarChart(
    List<TrialBalanceItem> data,
    ChartConfiguration? config,
  ) {
    final barGroups = data.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      final balance = item.openingBalance + item.totalDebit - item.totalCredit;

      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: balance.abs(),
            color: balance >= 0
                ? RevolutionaryColors.successGlow
                : RevolutionaryColors.errorCoral,
            width: 16,
            borderRadius: BorderRadius.circular(4),
          ),
        ],
      );
    }).toList();

    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: BarChart(
        BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY: data.isNotEmpty
              ? data
                        .map(
                          (e) =>
                              (e.openingBalance + e.totalDebit - e.totalCredit)
                                  .abs(),
                        )
                        .reduce((a, b) => a > b ? a : b) *
                    1.2
              : 100,
          barTouchData: BarTouchData(
            enabled: true,
            touchTooltipData: BarTouchTooltipData(
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                final item = data[groupIndex];
                final balance =
                    item.openingBalance + item.totalDebit - item.totalCredit;
                return BarTooltipItem(
                  '${item.name}\n${balance.toStringAsFixed(2)}',
                  const TextStyle(color: Colors.white, fontSize: 12),
                );
              },
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  if (value.toInt() < data.length) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        data[value.toInt()].code,
                        style: const TextStyle(fontSize: 10),
                      ),
                    );
                  }
                  return const Text('');
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 60,
                getTitlesWidget: (value, meta) {
                  return Text(
                    value.toStringAsFixed(0),
                    style: const TextStyle(fontSize: 10),
                  );
                },
              ),
            ),
            topTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            rightTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),
          borderData: FlBorderData(show: false),
          barGroups: barGroups,
          gridData: FlGridData(
            show: true,
            drawVerticalLine: false,
            horizontalInterval: data.isNotEmpty
                ? data
                          .map(
                            (e) =>
                                (e.openingBalance +
                                        e.totalDebit -
                                        e.totalCredit)
                                    .abs(),
                          )
                          .reduce((a, b) => a > b ? a : b) /
                      5
                : 20,
          ),
        ),
      ),
    );
  }

  /// رسم بياني دائري لميزان المراجعة
  static Widget _createTrialBalancePieChart(
    List<TrialBalanceItem> data,
    ChartConfiguration? config,
  ) {
    final positiveData = data.where((item) {
      final balance = item.openingBalance + item.totalDebit - item.totalCredit;
      return balance > 0;
    }).toList();

    if (positiveData.isEmpty) {
      return const Center(child: Text('لا توجد بيانات للعرض'));
    }

    final sections = positiveData.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      final balance = item.openingBalance + item.totalDebit - item.totalCredit;
      final total = positiveData.fold<double>(
        0,
        (sum, e) => sum + (e.openingBalance + e.totalDebit - e.totalCredit),
      );
      final percentage = (balance / total) * 100;

      return PieChartSectionData(
        color: _getChartColor(index),
        value: balance,
        title: '${percentage.toStringAsFixed(1)}%',
        radius: 100,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();

    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: PieChart(
        PieChartData(
          sections: sections,
          centerSpaceRadius: 40,
          sectionsSpace: 2,
          pieTouchData: PieTouchData(
            touchCallback: (FlTouchEvent event, pieTouchResponse) {
              // Handle touch events
            },
          ),
        ),
      ),
    );
  }

  /// رسم بياني خطي لميزان المراجعة
  static Widget _createTrialBalanceLineChart(
    List<TrialBalanceItem> data,
    ChartConfiguration? config,
  ) {
    final spots = data.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      final balance = item.openingBalance + item.totalDebit - item.totalCredit;
      return FlSpot(index.toDouble(), balance);
    }).toList();

    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: LineChart(
        LineChartData(
          gridData: FlGridData(
            show: true,
            drawVerticalLine: true,
            horizontalInterval: data.isNotEmpty
                ? data
                          .map(
                            (e) =>
                                (e.openingBalance +
                                        e.totalDebit -
                                        e.totalCredit)
                                    .abs(),
                          )
                          .reduce((a, b) => a > b ? a : b) /
                      5
                : 20,
            verticalInterval: 1,
          ),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                interval: 1,
                getTitlesWidget: (value, meta) {
                  if (value.toInt() < data.length) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        data[value.toInt()].code,
                        style: const TextStyle(fontSize: 10),
                      ),
                    );
                  }
                  return const Text('');
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 60,
                getTitlesWidget: (value, meta) {
                  return Text(
                    value.toStringAsFixed(0),
                    style: const TextStyle(fontSize: 10),
                  );
                },
              ),
            ),
            topTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            rightTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border.all(color: const Color(0xff37434d), width: 1),
          ),
          minX: 0,
          maxX: data.length.toDouble() - 1,
          minY: data.isNotEmpty
              ? data
                        .map(
                          (e) =>
                              e.openingBalance + e.totalDebit - e.totalCredit,
                        )
                        .reduce((a, b) => a < b ? a : b) *
                    1.1
              : -100,
          maxY: data.isNotEmpty
              ? data
                        .map(
                          (e) =>
                              e.openingBalance + e.totalDebit - e.totalCredit,
                        )
                        .reduce((a, b) => a > b ? a : b) *
                    1.1
              : 100,
          lineBarsData: [
            LineChartBarData(
              spots: spots,
              isCurved: true,
              color: RevolutionaryColors.damascusSky,
              barWidth: 3,
              isStrokeCapRound: true,
              dotData: FlDotData(
                show: true,
                getDotPainter: (spot, percent, barData, index) {
                  return FlDotCirclePainter(
                    radius: 4,
                    color: RevolutionaryColors.damascusSky,
                    strokeWidth: 2,
                    strokeColor: Colors.white,
                  );
                },
              ),
              belowBarData: BarAreaData(
                show: true,
                color: RevolutionaryColors.damascusSky.withValues(alpha: 0.3),
              ),
            ),
          ],
          lineTouchData: LineTouchData(
            touchTooltipData: LineTouchTooltipData(
              getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                return touchedBarSpots.map((barSpot) {
                  final item = data[barSpot.x.toInt()];
                  return LineTooltipItem(
                    '${item.name}\n${barSpot.y.toStringAsFixed(2)}',
                    const TextStyle(color: Colors.white, fontSize: 12),
                  );
                }).toList();
              },
            ),
          ),
        ),
      ),
    );
  }

  /// رسم بياني لقائمة الدخل
  static Widget _createProfitLossChart(
    ProfitLossReportData data,
    ChartType chartType,
    ChartConfiguration? config,
  ) {
    switch (chartType) {
      case ChartType.bar:
        return _createProfitLossBarChart(data, config);
      case ChartType.pie:
        return _createProfitLossPieChart(data, config);
      default:
        return _createProfitLossBarChart(data, config);
    }
  }

  /// رسم بياني أعمدة لقائمة الدخل
  static Widget _createProfitLossBarChart(
    ProfitLossReportData data,
    ChartConfiguration? config,
  ) {
    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: BarChart(
        BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY:
              [
                data.totalRevenue,
                data.totalExpense,
              ].reduce((a, b) => a > b ? a : b) *
              1.2,
          barTouchData: BarTouchData(
            enabled: true,
            touchTooltipData: BarTouchTooltipData(
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                final labels = ['الإيرادات', 'المصروفات', 'صافي الربح'];
                final values = [
                  data.totalRevenue,
                  data.totalExpense,
                  data.netProfit,
                ];
                return BarTooltipItem(
                  '${labels[groupIndex]}\n${values[groupIndex].toStringAsFixed(2)}',
                  const TextStyle(color: Colors.white, fontSize: 12),
                );
              },
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  const labels = ['الإيرادات', 'المصروفات', 'صافي الربح'];
                  if (value.toInt() < labels.length) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        labels[value.toInt()],
                        style: const TextStyle(fontSize: 10),
                      ),
                    );
                  }
                  return const Text('');
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 60,
                getTitlesWidget: (value, meta) {
                  return Text(
                    value.toStringAsFixed(0),
                    style: const TextStyle(fontSize: 10),
                  );
                },
              ),
            ),
            topTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            rightTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),
          borderData: FlBorderData(show: false),
          barGroups: [
            BarChartGroupData(
              x: 0,
              barRods: [
                BarChartRodData(
                  toY: data.totalRevenue,
                  color: RevolutionaryColors.successGlow,
                  width: 40,
                  borderRadius: BorderRadius.circular(4),
                ),
              ],
            ),
            BarChartGroupData(
              x: 1,
              barRods: [
                BarChartRodData(
                  toY: data.totalExpense,
                  color: RevolutionaryColors.errorCoral,
                  width: 40,
                  borderRadius: BorderRadius.circular(4),
                ),
              ],
            ),
            BarChartGroupData(
              x: 2,
              barRods: [
                BarChartRodData(
                  toY: data.netProfit.abs(),
                  color: data.netProfit >= 0
                      ? RevolutionaryColors.successGlow
                      : RevolutionaryColors.errorCoral,
                  width: 40,
                  borderRadius: BorderRadius.circular(4),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// رسم بياني دائري لقائمة الدخل
  static Widget _createProfitLossPieChart(
    ProfitLossReportData data,
    ChartConfiguration? config,
  ) {
    final total = data.totalRevenue + data.totalExpense;
    if (total == 0) {
      return const Center(child: Text('لا توجد بيانات للعرض'));
    }

    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: PieChart(
        PieChartData(
          sections: [
            PieChartSectionData(
              color: RevolutionaryColors.successGlow,
              value: data.totalRevenue,
              title:
                  'إيرادات\n${((data.totalRevenue / total) * 100).toStringAsFixed(1)}%',
              radius: 100,
              titleStyle: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            PieChartSectionData(
              color: RevolutionaryColors.errorCoral,
              value: data.totalExpense,
              title:
                  'مصروفات\n${((data.totalExpense / total) * 100).toStringAsFixed(1)}%',
              radius: 100,
              titleStyle: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ],
          centerSpaceRadius: 40,
          sectionsSpace: 2,
        ),
      ),
    );
  }

  /// الحصول على لون للرسم البياني
  static Color _getChartColor(int index) {
    final colors = [
      RevolutionaryColors.damascusSky,
      RevolutionaryColors.successGlow,
      RevolutionaryColors.warningAmber,
      RevolutionaryColors.errorCoral,
      RevolutionaryColors.infoTurquoise,
      Colors.purple,
      Colors.orange,
      Colors.teal,
      Colors.indigo,
    ];
    return colors[index % colors.length];
  }

  /// رسم بياني افتراضي
  static Widget _createDefaultChart() {
    return const Center(child: Text('نوع الرسم البياني غير مدعوم'));
  }

  /// رسم بياني للخطأ
  static Widget _createErrorChart(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error, size: 64, color: RevolutionaryColors.errorCoral),
          const SizedBox(height: 16),
          Text(
            'خطأ في إنشاء الرسم البياني',
            style: TextStyle(
              color: RevolutionaryColors.errorCoral,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: const TextStyle(color: Colors.grey, fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// رسم بياني للميزانية العمومية
  static Widget _createBalanceSheetChart(
    BalanceSheetReportData data,
    ChartType chartType,
    ChartConfiguration? config,
  ) {
    switch (chartType) {
      case ChartType.bar:
        return _createBalanceSheetBarChart(data, config);
      case ChartType.pie:
        return _createBalanceSheetPieChart(data, config);
      default:
        return _createBalanceSheetBarChart(data, config);
    }
  }

  /// رسم بياني أعمدة للميزانية العمومية
  static Widget _createBalanceSheetBarChart(
    BalanceSheetReportData data,
    ChartConfiguration? config,
  ) {
    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: BarChart(
        BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY:
              [
                data.totalAssets,
                data.totalLiabilities,
                data.totalEquity,
              ].reduce((a, b) => a > b ? a : b) *
              1.2,
          barTouchData: BarTouchData(
            enabled: true,
            touchTooltipData: BarTouchTooltipData(
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                final labels = ['الأصول', 'الخصوم', 'حقوق الملكية'];
                final values = [
                  data.totalAssets,
                  data.totalLiabilities,
                  data.totalEquity,
                ];
                return BarTooltipItem(
                  '${labels[groupIndex]}\n${values[groupIndex].toStringAsFixed(2)}',
                  const TextStyle(color: Colors.white, fontSize: 12),
                );
              },
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  const labels = ['الأصول', 'الخصوم', 'حقوق الملكية'];
                  if (value.toInt() < labels.length) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        labels[value.toInt()],
                        style: const TextStyle(fontSize: 10),
                      ),
                    );
                  }
                  return const Text('');
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 60,
                getTitlesWidget: (value, meta) {
                  return Text(
                    value.toStringAsFixed(0),
                    style: const TextStyle(fontSize: 10),
                  );
                },
              ),
            ),
            topTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            rightTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),
          borderData: FlBorderData(show: false),
          barGroups: [
            BarChartGroupData(
              x: 0,
              barRods: [
                BarChartRodData(
                  toY: data.totalAssets,
                  color: RevolutionaryColors.damascusSky,
                  width: 40,
                  borderRadius: BorderRadius.circular(4),
                ),
              ],
            ),
            BarChartGroupData(
              x: 1,
              barRods: [
                BarChartRodData(
                  toY: data.totalLiabilities,
                  color: RevolutionaryColors.warningAmber,
                  width: 40,
                  borderRadius: BorderRadius.circular(4),
                ),
              ],
            ),
            BarChartGroupData(
              x: 2,
              barRods: [
                BarChartRodData(
                  toY: data.totalEquity,
                  color: RevolutionaryColors.cedarsGreen,
                  width: 40,
                  borderRadius: BorderRadius.circular(4),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// رسم بياني دائري للميزانية العمومية
  static Widget _createBalanceSheetPieChart(
    BalanceSheetReportData data,
    ChartConfiguration? config,
  ) {
    final total = data.totalAssets;
    if (total == 0) {
      return const Center(child: Text('لا توجد بيانات للعرض'));
    }

    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: PieChart(
        PieChartData(
          sections: [
            PieChartSectionData(
              color: RevolutionaryColors.warningAmber,
              value: data.totalLiabilities,
              title:
                  'خصوم\n${((data.totalLiabilities / total) * 100).toStringAsFixed(1)}%',
              radius: 100,
              titleStyle: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            PieChartSectionData(
              color: RevolutionaryColors.successGlow,
              value: data.totalEquity,
              title:
                  'حقوق ملكية\n${((data.totalEquity / total) * 100).toStringAsFixed(1)}%',
              radius: 100,
              titleStyle: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ],
          centerSpaceRadius: 40,
          sectionsSpace: 2,
        ),
      ),
    );
  }

  /// رسم بياني لأعمار العملاء
  static Widget _createCustomerAgingChart(
    List<CustomerAgingItem> data,
    ChartType chartType,
    ChartConfiguration? config,
  ) {
    switch (chartType) {
      case ChartType.bar:
        return _createCustomerAgingBarChart(data, config);
      case ChartType.pie:
        return _createCustomerAgingPieChart(data, config);
      default:
        return _createCustomerAgingBarChart(data, config);
    }
  }

  /// رسم بياني أعمدة لأعمار العملاء
  static Widget _createCustomerAgingBarChart(
    List<CustomerAgingItem> data,
    ChartConfiguration? config,
  ) {
    if (data.isEmpty) {
      return const Center(child: Text('لا توجد بيانات للعرض'));
    }

    // حساب إجمالي المبالغ لكل فترة عمرية
    double totalCurrent = 0;
    double total31To60 = 0;
    double total61To90 = 0;
    double totalOver90 = 0;

    for (final item in data) {
      totalCurrent += item.currentAmount;
      total31To60 += item.days31To60;
      total61To90 += item.days61To90;
      totalOver90 += item.over90Days;
    }

    final maxValue = [
      totalCurrent,
      total31To60,
      total61To90,
      totalOver90,
    ].reduce((a, b) => a > b ? a : b);

    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: BarChart(
        BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY: maxValue * 1.2,
          barTouchData: BarTouchData(
            enabled: true,
            touchTooltipData: BarTouchTooltipData(
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                final labels = [
                  'حالي',
                  '31-60 يوم',
                  '61-90 يوم',
                  'أكثر من 90 يوم',
                ];
                final values = [
                  totalCurrent,
                  total31To60,
                  total61To90,
                  totalOver90,
                ];
                return BarTooltipItem(
                  '${labels[groupIndex]}\n${values[groupIndex].toStringAsFixed(2)}',
                  const TextStyle(color: Colors.white, fontSize: 12),
                );
              },
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  const labels = ['حالي', '31-60', '61-90', '+90'];
                  if (value.toInt() < labels.length) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        labels[value.toInt()],
                        style: const TextStyle(fontSize: 10),
                      ),
                    );
                  }
                  return const Text('');
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 60,
                getTitlesWidget: (value, meta) {
                  return Text(
                    value.toStringAsFixed(0),
                    style: const TextStyle(fontSize: 10),
                  );
                },
              ),
            ),
            topTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            rightTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),
          borderData: FlBorderData(show: false),
          barGroups: [
            BarChartGroupData(
              x: 0,
              barRods: [
                BarChartRodData(
                  toY: totalCurrent,
                  color: RevolutionaryColors.successGlow,
                  width: 40,
                  borderRadius: BorderRadius.circular(4),
                ),
              ],
            ),
            BarChartGroupData(
              x: 1,
              barRods: [
                BarChartRodData(
                  toY: total31To60,
                  color: RevolutionaryColors.warningAmber,
                  width: 40,
                  borderRadius: BorderRadius.circular(4),
                ),
              ],
            ),
            BarChartGroupData(
              x: 2,
              barRods: [
                BarChartRodData(
                  toY: total61To90,
                  color: Colors.orange,
                  width: 40,
                  borderRadius: BorderRadius.circular(4),
                ),
              ],
            ),
            BarChartGroupData(
              x: 3,
              barRods: [
                BarChartRodData(
                  toY: totalOver90,
                  color: RevolutionaryColors.errorCoral,
                  width: 40,
                  borderRadius: BorderRadius.circular(4),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// رسم بياني دائري لأعمار العملاء
  static Widget _createCustomerAgingPieChart(
    List<CustomerAgingItem> data,
    ChartConfiguration? config,
  ) {
    if (data.isEmpty) {
      return const Center(child: Text('لا توجد بيانات للعرض'));
    }

    // حساب إجمالي المبالغ لكل فترة عمرية
    double totalCurrent = 0;
    double total31To60 = 0;
    double total61To90 = 0;
    double totalOver90 = 0;

    for (final item in data) {
      totalCurrent += item.currentAmount;
      total31To60 += item.days31To60;
      total61To90 += item.days61To90;
      totalOver90 += item.over90Days;
    }

    final grandTotal = totalCurrent + total31To60 + total61To90 + totalOver90;
    if (grandTotal == 0) {
      return const Center(child: Text('لا توجد بيانات للعرض'));
    }

    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: PieChart(
        PieChartData(
          sections: [
            if (totalCurrent > 0)
              PieChartSectionData(
                color: RevolutionaryColors.successGlow,
                value: totalCurrent,
                title:
                    'حالي\n${((totalCurrent / grandTotal) * 100).toStringAsFixed(1)}%',
                radius: 100,
                titleStyle: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            if (total31To60 > 0)
              PieChartSectionData(
                color: RevolutionaryColors.warningAmber,
                value: total31To60,
                title:
                    '31-60\n${((total31To60 / grandTotal) * 100).toStringAsFixed(1)}%',
                radius: 100,
                titleStyle: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            if (total61To90 > 0)
              PieChartSectionData(
                color: Colors.orange,
                value: total61To90,
                title:
                    '61-90\n${((total61To90 / grandTotal) * 100).toStringAsFixed(1)}%',
                radius: 100,
                titleStyle: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            if (totalOver90 > 0)
              PieChartSectionData(
                color: RevolutionaryColors.errorCoral,
                value: totalOver90,
                title:
                    '+90\n${((totalOver90 / grandTotal) * 100).toStringAsFixed(1)}%',
                radius: 100,
                titleStyle: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
          ],
          centerSpaceRadius: 40,
          sectionsSpace: 2,
        ),
      ),
    );
  }

  /// رسم بياني لتحليل المبيعات
  static Widget _createSalesAnalysisChart(
    List<SalesAnalysisItem> data,
    ChartType chartType,
    ChartConfiguration? config,
  ) {
    switch (chartType) {
      case ChartType.bar:
        return _createSalesAnalysisBarChart(data, config);
      case ChartType.line:
        return _createSalesAnalysisLineChart(data, config);
      case ChartType.pie:
        return _createSalesAnalysisPieChart(data, config);
      default:
        return _createSalesAnalysisLineChart(data, config);
    }
  }

  /// رسم بياني خطي لتحليل المبيعات
  static Widget _createSalesAnalysisLineChart(
    List<SalesAnalysisItem> data,
    ChartConfiguration? config,
  ) {
    if (data.isEmpty) {
      return const Center(child: Text('لا توجد بيانات للعرض'));
    }

    final spots = data.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      return FlSpot(index.toDouble(), item.totalSales);
    }).toList();

    final maxSales = data
        .map((e) => e.totalSales)
        .reduce((a, b) => a > b ? a : b);
    final minSales = data
        .map((e) => e.totalSales)
        .reduce((a, b) => a < b ? a : b);

    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: LineChart(
        LineChartData(
          gridData: FlGridData(
            show: true,
            drawVerticalLine: true,
            horizontalInterval: maxSales > 0 ? maxSales / 5 : 20,
            verticalInterval: 1,
          ),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                interval: 1,
                getTitlesWidget: (value, meta) {
                  if (value.toInt() < data.length) {
                    final date = data[value.toInt()].saleDate;
                    return Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        date.substring(5), // عرض الشهر واليوم فقط
                        style: const TextStyle(fontSize: 10),
                      ),
                    );
                  }
                  return const Text('');
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 60,
                getTitlesWidget: (value, meta) {
                  return Text(
                    value.toStringAsFixed(0),
                    style: const TextStyle(fontSize: 10),
                  );
                },
              ),
            ),
            topTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            rightTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border.all(color: const Color(0xff37434d), width: 1),
          ),
          minX: 0,
          maxX: data.length.toDouble() - 1,
          minY: minSales * 0.9,
          maxY: maxSales * 1.1,
          lineBarsData: [
            LineChartBarData(
              spots: spots,
              isCurved: true,
              color: RevolutionaryColors.damascusSky,
              barWidth: 3,
              isStrokeCapRound: true,
              dotData: FlDotData(
                show: true,
                getDotPainter: (spot, percent, barData, index) {
                  return FlDotCirclePainter(
                    radius: 4,
                    color: RevolutionaryColors.damascusSky,
                    strokeWidth: 2,
                    strokeColor: Colors.white,
                  );
                },
              ),
              belowBarData: BarAreaData(
                show: true,
                color: RevolutionaryColors.damascusSky.withValues(alpha: 0.3),
              ),
            ),
          ],
          lineTouchData: LineTouchData(
            touchTooltipData: LineTouchTooltipData(
              getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                return touchedBarSpots.map((barSpot) {
                  final item = data[barSpot.x.toInt()];
                  return LineTooltipItem(
                    '${item.saleDate}\nالمبيعات: ${barSpot.y.toStringAsFixed(2)}\nعدد الفواتير: ${item.invoiceCount}',
                    const TextStyle(color: Colors.white, fontSize: 12),
                  );
                }).toList();
              },
            ),
          ),
        ),
      ),
    );
  }

  /// رسم بياني أعمدة لتحليل المبيعات
  static Widget _createSalesAnalysisBarChart(
    List<SalesAnalysisItem> data,
    ChartConfiguration? config,
  ) {
    if (data.isEmpty) {
      return const Center(child: Text('لا توجد بيانات للعرض'));
    }

    final barGroups = data.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;

      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: item.totalSales,
            color: RevolutionaryColors.damascusSky,
            width: 16,
            borderRadius: BorderRadius.circular(4),
          ),
        ],
      );
    }).toList();

    final maxSales = data
        .map((e) => e.totalSales)
        .reduce((a, b) => a > b ? a : b);

    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: BarChart(
        BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY: maxSales * 1.2,
          barTouchData: BarTouchData(
            enabled: true,
            touchTooltipData: BarTouchTooltipData(
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                final item = data[groupIndex];
                return BarTooltipItem(
                  '${item.saleDate}\nالمبيعات: ${item.totalSales.toStringAsFixed(2)}\nعدد الفواتير: ${item.invoiceCount}',
                  const TextStyle(color: Colors.white, fontSize: 12),
                );
              },
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  if (value.toInt() < data.length) {
                    final date = data[value.toInt()].saleDate;
                    return Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        date.substring(5), // عرض الشهر واليوم فقط
                        style: const TextStyle(fontSize: 10),
                      ),
                    );
                  }
                  return const Text('');
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 60,
                getTitlesWidget: (value, meta) {
                  return Text(
                    value.toStringAsFixed(0),
                    style: const TextStyle(fontSize: 10),
                  );
                },
              ),
            ),
            topTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            rightTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),
          borderData: FlBorderData(show: false),
          barGroups: barGroups,
        ),
      ),
    );
  }

  /// رسم بياني دائري لتحليل المبيعات
  static Widget _createSalesAnalysisPieChart(
    List<SalesAnalysisItem> data,
    ChartConfiguration? config,
  ) {
    if (data.isEmpty) {
      return const Center(child: Text('لا توجد بيانات للعرض'));
    }

    final totalSales = data.fold<double>(
      0,
      (sum, item) => sum + item.totalSales,
    );
    if (totalSales == 0) {
      return const Center(child: Text('لا توجد بيانات للعرض'));
    }

    // أخذ أعلى 5 أيام مبيعات
    final sortedData = List<SalesAnalysisItem>.from(data)
      ..sort((a, b) => b.totalSales.compareTo(a.totalSales));
    final topData = sortedData.take(5).toList();
    final otherSales = sortedData
        .skip(5)
        .fold<double>(0, (sum, item) => sum + item.totalSales);

    final sections = topData.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      final percentage = (item.totalSales / totalSales) * 100;

      return PieChartSectionData(
        color: _getChartColor(index),
        value: item.totalSales,
        title:
            '${item.saleDate.substring(5)}\n${percentage.toStringAsFixed(1)}%',
        radius: 100,
        titleStyle: const TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();

    // إضافة قسم "أخرى" إذا كان هناك بيانات إضافية
    if (otherSales > 0) {
      final otherPercentage = (otherSales / totalSales) * 100;
      sections.add(
        PieChartSectionData(
          color: Colors.grey,
          value: otherSales,
          title: 'أخرى\n${otherPercentage.toStringAsFixed(1)}%',
          radius: 100,
          titleStyle: const TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    }

    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: PieChart(
        PieChartData(
          sections: sections,
          centerSpaceRadius: 40,
          sectionsSpace: 2,
        ),
      ),
    );
  }

  /// رسم بياني لتحليل المشتريات
  static Widget _createPurchaseAnalysisChart(
    List<PurchaseAnalysisItem> data,
    ChartType chartType,
    ChartConfiguration? config,
  ) {
    switch (chartType) {
      case ChartType.bar:
        return _createPurchaseAnalysisBarChart(data, config);
      case ChartType.line:
        return _createPurchaseAnalysisLineChart(data, config);
      case ChartType.pie:
        return _createPurchaseAnalysisPieChart(data, config);
      default:
        return _createPurchaseAnalysisLineChart(data, config);
    }
  }

  /// رسم بياني خطي لتحليل المشتريات
  static Widget _createPurchaseAnalysisLineChart(
    List<PurchaseAnalysisItem> data,
    ChartConfiguration? config,
  ) {
    if (data.isEmpty) {
      return const Center(child: Text('لا توجد بيانات للعرض'));
    }

    final spots = data.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      return FlSpot(index.toDouble(), item.totalPurchases);
    }).toList();

    final maxPurchases = data
        .map((e) => e.totalPurchases)
        .reduce((a, b) => a > b ? a : b);
    final minPurchases = data
        .map((e) => e.totalPurchases)
        .reduce((a, b) => a < b ? a : b);

    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: LineChart(
        LineChartData(
          gridData: FlGridData(
            show: true,
            drawVerticalLine: true,
            horizontalInterval: maxPurchases > 0 ? maxPurchases / 5 : 20,
            verticalInterval: 1,
          ),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                interval: 1,
                getTitlesWidget: (value, meta) {
                  if (value.toInt() < data.length) {
                    final date = data[value.toInt()].purchaseDate;
                    return Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        date.substring(5), // عرض الشهر واليوم فقط
                        style: const TextStyle(fontSize: 10),
                      ),
                    );
                  }
                  return const Text('');
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 60,
                getTitlesWidget: (value, meta) {
                  return Text(
                    value.toStringAsFixed(0),
                    style: const TextStyle(fontSize: 10),
                  );
                },
              ),
            ),
            topTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            rightTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border.all(color: const Color(0xff37434d), width: 1),
          ),
          minX: 0,
          maxX: data.length.toDouble() - 1,
          minY: minPurchases * 0.9,
          maxY: maxPurchases * 1.1,
          lineBarsData: [
            LineChartBarData(
              spots: spots,
              isCurved: true,
              color: RevolutionaryColors.syrianGold,
              barWidth: 3,
              isStrokeCapRound: true,
              dotData: FlDotData(
                show: true,
                getDotPainter: (spot, percent, barData, index) {
                  return FlDotCirclePainter(
                    radius: 4,
                    color: RevolutionaryColors.syrianGold,
                    strokeWidth: 2,
                    strokeColor: Colors.white,
                  );
                },
              ),
              belowBarData: BarAreaData(
                show: true,
                color: RevolutionaryColors.syrianGold.withValues(alpha: 0.3),
              ),
            ),
          ],
          lineTouchData: LineTouchData(
            touchTooltipData: LineTouchTooltipData(
              getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                return touchedBarSpots.map((barSpot) {
                  final item = data[barSpot.x.toInt()];
                  return LineTooltipItem(
                    '${item.purchaseDate}\nالمشتريات: ${barSpot.y.toStringAsFixed(2)}\nعدد الفواتير: ${item.invoiceCount}',
                    const TextStyle(color: Colors.white, fontSize: 12),
                  );
                }).toList();
              },
            ),
          ),
        ),
      ),
    );
  }

  /// رسم بياني أعمدة لتحليل المشتريات
  static Widget _createPurchaseAnalysisBarChart(
    List<PurchaseAnalysisItem> data,
    ChartConfiguration? config,
  ) {
    if (data.isEmpty) {
      return const Center(child: Text('لا توجد بيانات للعرض'));
    }

    final barGroups = data.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;

      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: item.totalPurchases,
            color: RevolutionaryColors.syrianGold,
            width: 16,
            borderRadius: BorderRadius.circular(4),
          ),
        ],
      );
    }).toList();

    final maxPurchases = data
        .map((e) => e.totalPurchases)
        .reduce((a, b) => a > b ? a : b);

    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: BarChart(
        BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY: maxPurchases * 1.2,
          barTouchData: BarTouchData(
            enabled: true,
            touchTooltipData: BarTouchTooltipData(
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                final item = data[groupIndex];
                return BarTooltipItem(
                  '${item.purchaseDate}\nالمشتريات: ${item.totalPurchases.toStringAsFixed(2)}\nعدد الفواتير: ${item.invoiceCount}',
                  const TextStyle(color: Colors.white, fontSize: 12),
                );
              },
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  if (value.toInt() < data.length) {
                    final date = data[value.toInt()].purchaseDate;
                    return Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        date.substring(5), // عرض الشهر واليوم فقط
                        style: const TextStyle(fontSize: 10),
                      ),
                    );
                  }
                  return const Text('');
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 60,
                getTitlesWidget: (value, meta) {
                  return Text(
                    value.toStringAsFixed(0),
                    style: const TextStyle(fontSize: 10),
                  );
                },
              ),
            ),
            topTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            rightTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),
          borderData: FlBorderData(show: false),
          barGroups: barGroups,
        ),
      ),
    );
  }

  /// رسم بياني دائري لتحليل المشتريات
  static Widget _createPurchaseAnalysisPieChart(
    List<PurchaseAnalysisItem> data,
    ChartConfiguration? config,
  ) {
    if (data.isEmpty) {
      return const Center(child: Text('لا توجد بيانات للعرض'));
    }

    final totalPurchases = data.fold<double>(
      0,
      (sum, item) => sum + item.totalPurchases,
    );
    if (totalPurchases == 0) {
      return const Center(child: Text('لا توجد بيانات للعرض'));
    }

    // أخذ أعلى 5 أيام مشتريات
    final sortedData = List<PurchaseAnalysisItem>.from(data)
      ..sort((a, b) => b.totalPurchases.compareTo(a.totalPurchases));
    final topData = sortedData.take(5).toList();
    final otherPurchases = sortedData
        .skip(5)
        .fold<double>(0, (sum, item) => sum + item.totalPurchases);

    final sections = topData.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      final percentage = (item.totalPurchases / totalPurchases) * 100;

      return PieChartSectionData(
        color: _getChartColor(index),
        value: item.totalPurchases,
        title:
            '${item.purchaseDate.substring(5)}\n${percentage.toStringAsFixed(1)}%',
        radius: 100,
        titleStyle: const TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();

    // إضافة قسم "أخرى" إذا كان هناك بيانات إضافية
    if (otherPurchases > 0) {
      final otherPercentage = (otherPurchases / totalPurchases) * 100;
      sections.add(
        PieChartSectionData(
          color: Colors.grey,
          value: otherPurchases,
          title: 'أخرى\n${otherPercentage.toStringAsFixed(1)}%',
          radius: 100,
          titleStyle: const TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    }

    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: PieChart(
        PieChartData(
          sections: sections,
          centerSpaceRadius: 40,
          sectionsSpace: 2,
        ),
      ),
    );
  }

  /// رسم بياني للمخزون
  static Widget _createInventoryChart(
    List<InventoryReportItem> data,
    ChartType chartType,
    ChartConfiguration? config,
  ) {
    switch (chartType) {
      case ChartType.bar:
        return _createInventoryBarChart(data, config);
      case ChartType.pie:
        return _createInventoryPieChart(data, config);
      default:
        return _createInventoryBarChart(data, config);
    }
  }

  /// رسم بياني أعمدة للمخزون
  static Widget _createInventoryBarChart(
    List<InventoryReportItem> data,
    ChartConfiguration? config,
  ) {
    if (data.isEmpty) {
      return const Center(child: Text('لا توجد بيانات للعرض'));
    }

    // أخذ أعلى 10 عناصر حسب القيمة الإجمالية
    final sortedData = List<InventoryReportItem>.from(data)
      ..sort((a, b) => b.totalCostValue.compareTo(a.totalCostValue));
    final topData = sortedData.take(10).toList();

    final barGroups = topData.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;

      Color barColor;
      switch (item.stockStatus) {
        case 'out_of_stock':
          barColor = RevolutionaryColors.errorCoral;
          break;
        case 'low_stock':
          barColor = RevolutionaryColors.warningAmber;
          break;
        default:
          barColor = RevolutionaryColors.successGlow;
      }

      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: item.totalCostValue,
            color: barColor,
            width: 16,
            borderRadius: BorderRadius.circular(4),
          ),
        ],
      );
    }).toList();

    final maxValue = topData
        .map((e) => e.totalCostValue)
        .reduce((a, b) => a > b ? a : b);

    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: BarChart(
        BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY: maxValue * 1.2,
          barTouchData: BarTouchData(
            enabled: true,
            touchTooltipData: BarTouchTooltipData(
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                final item = topData[groupIndex];
                final statusText = item.stockStatus == 'out_of_stock'
                    ? 'نفد المخزون'
                    : item.stockStatus == 'low_stock'
                    ? 'مخزون منخفض'
                    : 'متوفر';
                return BarTooltipItem(
                  '${item.name}\nالكمية: ${item.quantity.toStringAsFixed(0)}\nالقيمة: ${item.totalCostValue.toStringAsFixed(2)}\nالحالة: $statusText',
                  const TextStyle(color: Colors.white, fontSize: 12),
                );
              },
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  if (value.toInt() < topData.length) {
                    final item = topData[value.toInt()];
                    return Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        item.code,
                        style: const TextStyle(fontSize: 10),
                      ),
                    );
                  }
                  return const Text('');
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 60,
                getTitlesWidget: (value, meta) {
                  return Text(
                    value.toStringAsFixed(0),
                    style: const TextStyle(fontSize: 10),
                  );
                },
              ),
            ),
            topTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            rightTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),
          borderData: FlBorderData(show: false),
          barGroups: barGroups,
        ),
      ),
    );
  }

  /// رسم بياني دائري للمخزون حسب الحالة
  static Widget _createInventoryPieChart(
    List<InventoryReportItem> data,
    ChartConfiguration? config,
  ) {
    if (data.isEmpty) {
      return const Center(child: Text('لا توجد بيانات للعرض'));
    }

    // تجميع البيانات حسب حالة المخزون
    double inStockValue = 0;
    double lowStockValue = 0;
    double outOfStockValue = 0;

    for (final item in data) {
      switch (item.stockStatus) {
        case 'out_of_stock':
          outOfStockValue += item.totalCostValue;
          break;
        case 'low_stock':
          lowStockValue += item.totalCostValue;
          break;
        default:
          inStockValue += item.totalCostValue;
      }
    }

    final totalValue = inStockValue + lowStockValue + outOfStockValue;
    if (totalValue == 0) {
      return const Center(child: Text('لا توجد بيانات للعرض'));
    }

    final sections = <PieChartSectionData>[];

    if (inStockValue > 0) {
      sections.add(
        PieChartSectionData(
          color: RevolutionaryColors.successGlow,
          value: inStockValue,
          title:
              'متوفر\n${((inStockValue / totalValue) * 100).toStringAsFixed(1)}%',
          radius: 100,
          titleStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    }

    if (lowStockValue > 0) {
      sections.add(
        PieChartSectionData(
          color: RevolutionaryColors.warningAmber,
          value: lowStockValue,
          title:
              'منخفض\n${((lowStockValue / totalValue) * 100).toStringAsFixed(1)}%',
          radius: 100,
          titleStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    }

    if (outOfStockValue > 0) {
      sections.add(
        PieChartSectionData(
          color: RevolutionaryColors.errorCoral,
          value: outOfStockValue,
          title:
              'نفد\n${((outOfStockValue / totalValue) * 100).toStringAsFixed(1)}%',
          radius: 100,
          titleStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    }

    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: PieChart(
        PieChartData(
          sections: sections,
          centerSpaceRadius: 40,
          sectionsSpace: 2,
        ),
      ),
    );
  }
}

/// أنواع الرسوم البيانية
enum ChartType { bar, line, pie, area, scatter, radar }

/// إعدادات الرسم البياني
class ChartConfiguration {
  final bool showLegend;
  final bool showTooltips;
  final bool showGrid;
  final Color? primaryColor;
  final Color? secondaryColor;
  final double? height;
  final double? width;
  final Map<String, dynamic> customSettings;

  const ChartConfiguration({
    this.showLegend = true,
    this.showTooltips = true,
    this.showGrid = true,
    this.primaryColor,
    this.secondaryColor,
    this.height,
    this.width,
    this.customSettings = const {},
  });

  factory ChartConfiguration.defaultConfig() {
    return const ChartConfiguration();
  }
}

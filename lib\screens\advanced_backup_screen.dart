/// شاشة إدارة النسخ الاحتياطية المتقدمة
/// تدير النسخ الاحتياطية المشفرة والمجدولة مع ميزات متقدمة
library;

import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/loading_widget.dart';
import '../responsive/responsive.dart';

class AdvancedBackupScreen extends StatefulWidget {
  const AdvancedBackupScreen({super.key});

  @override
  State<AdvancedBackupScreen> createState() => _AdvancedBackupScreenState();
}

class _AdvancedBackupScreenState extends State<AdvancedBackupScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  List<BackupInfo> _backups = [];
  bool _isLoading = true;
  bool _isCreatingBackup = false;

  // إعدادات النسخ الاحتياطية
  bool _autoBackupEnabled = true;
  String _backupFrequency = 'daily';
  int _retentionDays = 30;
  bool _encryptBackups = true;
  String _backupLocation = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadBackups();
    _loadSettings();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadBackups() async {
    setState(() => _isLoading = true);
    try {
      // محاكاة تحميل النسخ الاحتياطية
      await Future.delayed(const Duration(seconds: 1));
      final backups = <BackupInfo>[
        BackupInfo(
          name: 'backup_2025_07_15_001',
          createdAt: DateTime.now().subtract(const Duration(days: 1)),
          sizeBytes: 1024 * 1024 * 50, // 50 MB
          isEncrypted: true,
          description: 'نسخة احتياطية تلقائية',
          filePath: '/backups/backup_2025_07_15_001.db',
        ),
        BackupInfo(
          name: 'backup_2025_07_14_001',
          createdAt: DateTime.now().subtract(const Duration(days: 2)),
          sizeBytes: 1024 * 1024 * 48, // 48 MB
          isEncrypted: true,
          description: 'نسخة احتياطية يدوية',
          filePath: '/backups/backup_2025_07_14_001.db',
        ),
      ];

      setState(() {
        _backups = backups;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في تحميل النسخ الاحتياطية: $e');
    }
  }

  Future<void> _loadSettings() async {
    try {
      // محاكاة تحميل الإعدادات
      await Future.delayed(const Duration(milliseconds: 500));
      setState(() {
        _autoBackupEnabled = true;
        _backupFrequency = 'daily';
        _retentionDays = 30;
        _encryptBackups = true;
        _backupLocation = '';
      });
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل الإعدادات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const ResponsiveText.h2('إدارة النسخ الاحتياطية'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.backup), text: 'النسخ الاحتياطية'),
            Tab(icon: Icon(Icons.schedule), text: 'الجدولة'),
            Tab(icon: Icon(Icons.settings), text: 'الإعدادات'),
          ],
        ),
      ),
      body: _isLoading
          ? const LoadingWidget()
          : TabBarView(
              controller: _tabController,
              children: [
                _buildBackupsTab(),
                _buildScheduleTab(),
                _buildSettingsTab(),
              ],
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _isCreatingBackup ? null : _createBackup,
        backgroundColor: _isCreatingBackup
            ? Colors.grey
            : RevolutionaryColors.damascusSky,
        icon: _isCreatingBackup
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Icon(Icons.backup, color: Colors.white),
        label: Text(
          _isCreatingBackup ? 'جاري الإنشاء...' : 'نسخة احتياطية جديدة',
          style: const TextStyle(color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildBackupsTab() {
    return Column(
      children: [
        // إحصائيات النسخ الاحتياطية
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي النسخ',
                  _backups.length.toString(),
                  Icons.backup,
                  RevolutionaryColors.damascusSky,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'الحجم الإجمالي',
                  _formatFileSize(_getTotalBackupSize()),
                  Icons.storage,
                  RevolutionaryColors.successGlow,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'آخر نسخة',
                  _getLastBackupTime(),
                  Icons.access_time,
                  Colors.orange,
                ),
              ),
            ],
          ),
        ),

        // قائمة النسخ الاحتياطية
        Expanded(
          child: _backups.isEmpty
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.backup, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'لا توجد نسخ احتياطية',
                        style: TextStyle(fontSize: 18, color: Colors.grey),
                      ),
                      Text(
                        'اضغط على الزر أدناه لإنشاء نسخة احتياطية',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: _backups.length,
                  itemBuilder: (context, index) {
                    final backup = _backups[index];
                    return _buildBackupCard(backup);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildBackupCard(BackupInfo backup) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          backgroundColor: backup.isEncrypted
              ? RevolutionaryColors.damascusSky
              : Colors.orange,
          child: Icon(
            backup.isEncrypted ? Icons.lock : Icons.backup,
            color: Colors.white,
          ),
        ),
        title: Text(
          backup.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('التاريخ: ${_formatDateTime(backup.createdAt)}'),
            Text('الحجم: ${_formatFileSize(backup.sizeBytes)}'),
            Text('النوع: ${backup.isEncrypted ? 'مشفرة' : 'عادية'}'),
            if (backup.description.isNotEmpty)
              Text('الوصف: ${backup.description}'),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleBackupAction(value, backup),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'restore',
              child: ListTile(
                leading: Icon(Icons.restore),
                title: Text('استعادة'),
              ),
            ),
            const PopupMenuItem(
              value: 'download',
              child: ListTile(
                leading: Icon(Icons.download),
                title: Text('تحميل'),
              ),
            ),
            const PopupMenuItem(
              value: 'verify',
              child: ListTile(
                leading: Icon(Icons.verified),
                title: Text('التحقق'),
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete, color: Colors.red),
                title: Text('حذف', style: TextStyle(color: Colors.red)),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScheduleTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // تفعيل النسخ التلقائية
          Card(
            child: SwitchListTile(
              title: const Text('النسخ الاحتياطية التلقائية'),
              subtitle: const Text('تفعيل النسخ الاحتياطية المجدولة'),
              value: _autoBackupEnabled,
              onChanged: (value) {
                setState(() => _autoBackupEnabled = value);
                _saveSettings();
              },
              activeColor: RevolutionaryColors.damascusSky,
            ),
          ),

          const SizedBox(height: 16),

          // تكرار النسخ
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'تكرار النسخ الاحتياطية',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String>(
                    value: _backupFrequency,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'hourly', child: Text('كل ساعة')),
                      DropdownMenuItem(value: 'daily', child: Text('يومياً')),
                      DropdownMenuItem(
                        value: 'weekly',
                        child: Text('أسبوعياً'),
                      ),
                      DropdownMenuItem(value: 'monthly', child: Text('شهرياً')),
                    ],
                    onChanged: (value) {
                      setState(() => _backupFrequency = value!);
                      _saveSettings();
                    },
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // مدة الاحتفاظ
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'مدة الاحتفاظ بالنسخ (بالأيام)',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    initialValue: _retentionDays.toString(),
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      suffixText: 'يوم',
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      final days = int.tryParse(value) ?? 30;
                      setState(() => _retentionDays = days);
                      _saveSettings();
                    },
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // الجدولة التالية
          Card(
            child: ListTile(
              leading: const Icon(Icons.schedule),
              title: const Text('النسخة الاحتياطية التالية'),
              subtitle: Text(_getNextBackupTime()),
              trailing: IconButton(
                onPressed: _scheduleNextBackup,
                icon: const Icon(Icons.edit),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // تشفير النسخ
          Card(
            child: SwitchListTile(
              title: const Text('تشفير النسخ الاحتياطية'),
              subtitle: const Text('حماية النسخ بكلمة مرور'),
              value: _encryptBackups,
              onChanged: (value) {
                setState(() => _encryptBackups = value);
                _saveSettings();
              },
              activeColor: RevolutionaryColors.damascusSky,
            ),
          ),

          const SizedBox(height: 16),

          // مكان الحفظ
          Card(
            child: ListTile(
              leading: const Icon(Icons.folder),
              title: const Text('مكان حفظ النسخ'),
              subtitle: Text(
                _backupLocation.isEmpty ? 'المجلد الافتراضي' : _backupLocation,
              ),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _selectBackupLocation,
            ),
          ),

          const SizedBox(height: 16),

          // تنظيف النسخ القديمة
          Card(
            child: ListTile(
              leading: const Icon(Icons.cleaning_services),
              title: const Text('تنظيف النسخ القديمة'),
              subtitle: const Text('حذف النسخ الأقدم من المدة المحددة'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _cleanOldBackups,
            ),
          ),

          const SizedBox(height: 16),

          // اختبار النسخ الاحتياطية
          Card(
            child: ListTile(
              leading: const Icon(Icons.verified),
              title: const Text('اختبار النسخ الاحتياطية'),
              subtitle: const Text('التحقق من سلامة جميع النسخ'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _testAllBackups,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: color.withValues(alpha: 0.8)),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Future<void> _createBackup() async {
    setState(() => _isCreatingBackup = true);
    try {
      // محاكاة إنشاء النسخة الاحتياطية
      await Future.delayed(const Duration(seconds: 3));

      await _loadBackups();
      _showSuccessSnackBar('تم إنشاء النسخة الاحتياطية بنجاح');
    } catch (e) {
      _showErrorSnackBar('خطأ في إنشاء النسخة الاحتياطية: $e');
    } finally {
      setState(() => _isCreatingBackup = false);
    }
  }

  void _handleBackupAction(String action, BackupInfo backup) {
    switch (action) {
      case 'restore':
        _restoreBackup(backup);
        break;
      case 'download':
        _downloadBackup(backup);
        break;
      case 'verify':
        _verifyBackup(backup);
        break;
      case 'delete':
        _deleteBackup(backup);
        break;
    }
  }

  Future<void> _restoreBackup(BackupInfo backup) async {
    // TODO: تنفيذ استعادة النسخة الاحتياطية
    _showInfoSnackBar('ميزة الاستعادة قيد التطوير');
  }

  Future<void> _downloadBackup(BackupInfo backup) async {
    // TODO: تنفيذ تحميل النسخة الاحتياطية
    _showInfoSnackBar('ميزة التحميل قيد التطوير');
  }

  Future<void> _verifyBackup(BackupInfo backup) async {
    // TODO: تنفيذ التحقق من النسخة الاحتياطية
    _showInfoSnackBar('ميزة التحقق قيد التطوير');
  }

  Future<void> _deleteBackup(BackupInfo backup) async {
    // TODO: تنفيذ حذف النسخة الاحتياطية
    _showInfoSnackBar('ميزة الحذف قيد التطوير');
  }

  Future<void> _saveSettings() async {
    try {
      // محاكاة حفظ الإعدادات
      await Future.delayed(const Duration(milliseconds: 500));
      _showSuccessSnackBar('تم حفظ الإعدادات بنجاح');
    } catch (e) {
      _showErrorSnackBar('خطأ في حفظ الإعدادات: $e');
    }
  }

  void _selectBackupLocation() {
    // TODO: تنفيذ اختيار مكان الحفظ
    _showInfoSnackBar('ميزة اختيار المكان قيد التطوير');
  }

  void _cleanOldBackups() {
    // TODO: تنفيذ تنظيف النسخ القديمة
    _showInfoSnackBar('ميزة التنظيف قيد التطوير');
  }

  void _testAllBackups() {
    // TODO: تنفيذ اختبار جميع النسخ
    _showInfoSnackBar('ميزة الاختبار قيد التطوير');
  }

  void _scheduleNextBackup() {
    // TODO: تنفيذ جدولة النسخة التالية
    _showInfoSnackBar('ميزة الجدولة قيد التطوير');
  }

  int _getTotalBackupSize() {
    return _backups.fold(0, (sum, backup) => sum + backup.sizeBytes);
  }

  String _getLastBackupTime() {
    if (_backups.isEmpty) return 'لا يوجد';
    final latest = _backups.reduce(
      (a, b) => a.createdAt.isAfter(b.createdAt) ? a : b,
    );
    return _formatDateTime(latest.createdAt);
  }

  String _getNextBackupTime() {
    // TODO: حساب وقت النسخة التالية
    return 'غير محدد';
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.successGlow,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.damascusSky,
      ),
    );
  }
}

// نموذج معلومات النسخة الاحتياطية
class BackupInfo {
  final String name;
  final DateTime createdAt;
  final int sizeBytes;
  final bool isEncrypted;
  final String description;
  final String filePath;

  const BackupInfo({
    required this.name,
    required this.createdAt,
    required this.sizeBytes,
    required this.isEncrypted,
    required this.description,
    required this.filePath,
  });
}
